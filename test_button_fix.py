#!/usr/bin/env python3
"""
Test script to verify button text display fix
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_text():
    """Test that button text is properly displayed"""
    print("Testing button text display fix...")
    
    try:
        from Deep_Uninstaller_Pro import CompleteSoftwareUninstaller
        
        # Create instance
        app = CompleteSoftwareUninstaller()
        
        # Test that find_in_log method exists and is callable
        if hasattr(app, 'find_in_log') and callable(getattr(app, 'find_in_log')):
            print("✅ find_in_log method exists and is callable")
        else:
            print("❌ find_in_log method missing or not callable")
            return False
        
        # Test that open_settings method exists and is enhanced
        if hasattr(app, 'open_settings') and callable(getattr(app, 'open_settings')):
            print("✅ open_settings method exists and is callable")
        else:
            print("❌ open_settings method missing or not callable")
            return False
        
        # Test that new settings methods exist
        new_methods = ['save_current_settings', 'reset_to_defaults']
        for method in new_methods:
            if hasattr(app, method) and callable(getattr(app, method)):
                print(f"✅ {method} method exists and is callable")
            else:
                print(f"❌ {method} method missing or not callable")
                return False
        
        print("✅ All button-related methods are properly implemented")
        return True
        
    except Exception as e:
        print(f"❌ Error testing button fix: {e}")
        return False

def main():
    """Main test function"""
    print("Button Text Display Fix - Test")
    print("=" * 40)
    
    if test_button_text():
        print("\n✅ SUCCESS: Button text display fix verified!")
        print("\nThe 'Find in Log' dialog should now display:")
        print("• 🔍 Search button")
        print("• 🧹 Clear button") 
        print("• ❌ Cancel button")
        print("\nThe Settings dialog should now display:")
        print("• 💾 Save Settings button")
        print("• 🔄 Reset to Defaults button")
        print("• ❌ Close button")
    else:
        print("\n❌ FAILED: Button text display fix verification failed!")
    
    return True

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
