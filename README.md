# Deep Uninstaller Pro v1.1.0 by AMSSoftX

A comprehensive, professional-grade software uninstaller for Windows that completely removes all traces of installed programs. Features an enhanced modern GUI, real-time progress tracking, advanced cleanup options, and comprehensive system integration removal.

## ✨ Features

### 🎯 Core Uninstall Features
- **Complete File Removal**: Removes all program files and folders from common locations
- **Deep Registry Cleanup**: Comprehensive scan and removal of registry entries across all hives
- **Service Management**: Stops and removes related Windows services
- **Scheduled Task Cleanup**: Removes associated scheduled tasks
- **Advanced Process Termination**: Intelligent process killing including child processes and window-based detection

### 🔧 Advanced Deep Cleaning
- **API Hooks Removal**: Cleans Windows API hooks and injected DLLs
- **DLL Unregistration**: Unregisters and removes DLL registrations with system file checking
- **COM Objects Cleanup**: Removes COM objects, interfaces, and type libraries
- **File Associations**: Cleans file type associations and default programs
- **Context Menu Entries**: Removes shell extensions and context menu items
- **Firewall Rules**: Removes Windows Firewall rules created by software
- **Network Connections**: Terminates active connections and cleans network profiles
- **Startup Entries**: Removes autorun entries from registry, startup folders, and task scheduler
- **System Integration**: Deep system-level cleanup including drivers and kernel components

### 🛡️ Safety & Backup Features
- **Registry Backup**: Automatic registry backup creation before major changes
- **System Restore Points**: Create restore points before uninstallation
- **Force Delete Technology**: Advanced file deletion with permission changes and reboot scheduling
- **Safe Mode**: Conservative cleanup mode for critical system components
- **Process Protection**: Handles locked files by terminating using processes
- **Verification System**: Post-uninstall verification to ensure complete removal

### 🎨 Enhanced User Interface
- **Modern Tabbed Interface**: Organized options in Basic and Advanced tabs
- **Real-time Progress Tracking**: Detailed progress bars with percentage and operation status
- **Colored Logging System**: Color-coded log messages (success, warning, error, info)
- **Software Search & Filter**: Quick search and filter installed programs
- **Scan Report Generation**: Detailed reports of what will be removed
- **Menu Bar with Tools**: Comprehensive menu with utilities and help

### 🔧 Professional Tools
- **Registry Backup Tool**: Create full registry backups
- **System Information**: Detailed system and hardware information
- **Temp File Cleanup**: Clean temporary files and folders
- **Disk Cleanup Integration**: Launch Windows Disk Cleanup
- **Settings Management**: Save and load uninstaller configurations
- **Log Export**: Save operation logs to files

### ⌨️ Productivity Features
- **Keyboard Shortcuts**: F1 (Help), F5 (Refresh), Ctrl+S (Save Log)
- **Auto-scroll Logging**: Automatic log scrolling with toggle option
- **Find in Log**: Search functionality within operation logs
- **Copy to Clipboard**: Easy log copying for support
- **Browse for Software**: Manual software selection via file browser

## Requirements

### Required Python Modules
- Python 3.6 or higher
- `psutil` - Process and system utilities
- `tkinter` - GUI framework (usually included with Python)

### Optional Windows Modules (for enhanced functionality)
- `pywin32` - Windows API access for advanced features
  - `win32gui`, `win32process`, `win32api`, `win32con`
  - `win32service`, `win32serviceutil`

## Installation

1. **Install Python 3.6+** from [python.org](https://python.org)

2. **Install required dependencies**:
   ```bash
   pip install psutil
   ```

3. **Install optional Windows modules** (recommended):
   ```bash
   pip install pywin32
   ```

4. **Download the application**:
   - Download `Deep_Uninstaller_Pro.py`
   - Optionally download `test_uninstaller.py` for testing

## Usage

### Running the Application
```bash
python Deep_Uninstaller_Pro.py
```

### Testing the Installation
```bash
python test_uninstaller.py
```

### Using the Enhanced Application

#### 🚀 Quick Start
1. **Launch** the application with administrator privileges (recommended)
2. **Select Software**: Choose from the dropdown or use the search filter
3. **Configure Options**: Use the tabbed interface (Basic/Advanced)
4. **Scan First**: Click "🔍 Scan Software" to analyze components
5. **Review Report**: Check the scan report for detailed information
6. **Uninstall**: Click "🗑️ Complete Uninstall" to remove the software
7. **Monitor Progress**: Watch real-time progress and colored log messages

#### 📋 Detailed Workflow
1. **Software Selection**:
   - Use the dropdown to select installed software
   - Type to filter/search the software list
   - Use "📁 Browse" to manually select software executables
   - Click "🔄 Refresh" to update the installed programs list

2. **Configuration Options**:
   - **Basic Tab**: Standard cleanup options (files, registry, services, tasks)
   - **Advanced Tab**: Deep cleaning options (API hooks, DLLs, COM objects, etc.)
   - **Quick Selection**: Use "Select All", "Select None", "Basic Only", "Advanced Only"
   - **Safety Options**: Enable registry backup and safe mode

3. **Scanning Process**:
   - Click "🔍 Scan Software" to analyze the selected program
   - Monitor real-time progress with percentage indicators
   - Review detailed scan results in the operation log
   - Use "📊 View Report" to see a comprehensive scan summary

4. **Uninstallation Process**:
   - Click "🗑️ Complete Uninstall" after reviewing scan results
   - Confirm the uninstall operation in the dialog
   - Monitor step-by-step progress with detailed status updates
   - Review the colored log messages for operation results

5. **Post-Uninstall**:
   - Automatic verification of removal completeness
   - Software list automatically refreshes
   - Success confirmation with summary
   - Option to save operation log for records

## Safety Warnings

⚠️ **IMPORTANT SAFETY NOTICES**:

- **Run as Administrator**: Required for full functionality
- **Create System Backup**: Always backup your system before using
- **Close Target Software**: Ensure the software to uninstall is completely closed
- **Review Scan Results**: Check what will be removed before proceeding
- **System Critical Software**: Do not uninstall Windows system components
- **Antivirus Software**: Be careful when uninstalling security software

## Advanced Features

### Force Delete Technology
- Terminates processes using locked files
- Changes file permissions and attributes
- Schedules deletion on next reboot if immediate deletion fails

### Registry Deep Scan
- Scans multiple registry hives (HKLM, HKCU, HKU)
- Recursive scanning with configurable depth
- Safe deletion with error handling

### Process Management
- Identifies related processes by name, path, and command line
- Terminates child processes first
- Uses both graceful termination and force kill
- Window-based process identification

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**:
   - Run as Administrator
   - Disable antivirus temporarily
   - Close the target software completely

2. **Files Cannot Be Deleted**:
   - Files will be scheduled for deletion on reboot
   - Check if processes are still running
   - Restart and run the uninstaller again

3. **Registry Access Denied**:
   - Ensure administrator privileges
   - Some system keys are protected and cannot be modified

### Log Analysis
- All operations are logged in real-time
- Check the log for detailed error messages
- Use "Clear Log" to start fresh

## Developer Information

**Developed by**: AMSSoftX  
**Website**: https://amssoftx.com  
**Version**: 1.0  
**License**: Proprietary

## Technical Details

### Architecture
- **GUI Framework**: Tkinter with modern styling
- **Process Management**: psutil library
- **Registry Operations**: winreg module
- **File Operations**: shutil with custom error handlers
- **Threading**: Background operations to prevent GUI freezing

### Supported Windows Versions
- Windows 10
- Windows 11
- Windows Server 2016+
- Earlier versions may work but are not officially supported

## Disclaimer

This software is provided "as is" without warranty. Use at your own risk. Always backup your system before performing uninstall operations. The developers are not responsible for any system damage or data loss.
