#!/usr/bin/env python3
"""
Test script to verify scrollable left panel implementation
"""

import sys
import os
import tkinter as tk

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scrollable_interface():
    """Test that scrollable interface is properly implemented"""
    print("Testing scrollable left panel implementation...")
    
    try:
        from Deep_Uninstaller_Pro import CompleteSoftwareUninstaller
        
        # Create instance
        app = CompleteSoftwareUninstaller()
        
        # Test that new advanced options variables exist
        new_variables = [
            'remove_certificates', 'remove_themes', 'remove_browser_extensions',
            'remove_device_drivers', 'remove_codecs', 'remove_printer_drivers',
            'remove_fonts', 'remove_language_packs', 'remove_temp_files', 'remove_cache_files'
        ]
        
        for var_name in new_variables:
            if hasattr(app, var_name):
                var = getattr(app, var_name)
                if isinstance(var, tk.BooleanVar):
                    print(f"✅ {var_name} variable exists and is BooleanVar")
                else:
                    print(f"❌ {var_name} exists but is not BooleanVar")
                    return False
            else:
                print(f"❌ {var_name} variable missing")
                return False
        
        print("✅ All new advanced option variables are properly implemented")
        return True
        
    except Exception as e:
        print(f"❌ Error testing scrollable interface: {e}")
        return False

def test_interface_layout():
    """Test interface layout improvements"""
    print("\nTesting interface layout improvements...")
    
    try:
        # Test that the interface can be created without errors
        root = tk.Tk()
        root.withdraw()  # Hide the test window
        
        from Deep_Uninstaller_Pro import CompleteSoftwareUninstaller
        app = CompleteSoftwareUninstaller()
        
        # Check if the main window was created
        if app.root and app.root.winfo_exists():
            print("✅ Main window created successfully")
            
            # Test window properties
            title = app.root.title()
            if "Deep Uninstaller Pro" in title:
                print("✅ Window title is correct")
            else:
                print(f"❌ Window title incorrect: {title}")
                return False
            
            # Clean up
            app.root.destroy()
            root.destroy()
            
            return True
        else:
            print("❌ Main window creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing interface layout: {e}")
        return False

def main():
    """Main test function"""
    print("Scrollable Left Panel - Test")
    print("=" * 40)
    
    success = True
    
    if not test_scrollable_interface():
        success = False
    
    if not test_interface_layout():
        success = False
    
    if success:
        print("\n✅ SUCCESS: Scrollable left panel implementation verified!")
        print("\nNew Features Added:")
        print("• 📜 Scrollable left panel with mouse wheel support")
        print("• 🔒 Security Certificates removal option")
        print("• 🎨 Themes & Skins removal option")
        print("• 🔌 Browser Extensions removal option")
        print("• 📱 Device Drivers removal option")
        print("• 🎵 Audio/Video Codecs removal option")
        print("• 🖨️ Printer Drivers removal option")
        print("• 🔤 Fonts removal option")
        print("• 🌍 Language Packs removal option")
        print("• 🗂️ Temp Files removal option")
        print("• 💾 Cache Files removal option")
        print("\nLayout Improvements:")
        print("• 📊 Organized options in logical categories")
        print("• 📏 Two-column layout for better space utilization")
        print("• 🎯 Clear section headers for easy navigation")
        print("• 📜 Vertical scrollbar for accessing all options")
    else:
        print("\n❌ FAILED: Scrollable left panel implementation verification failed!")
    
    return success

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
