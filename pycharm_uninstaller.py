#!/usr/bin/env python3
"""
Special PyCharm Uninstaller
This script specifically handles PyCharm Community Edition uninstallation issues
"""

import os
import sys
import shutil
import subprocess
import winreg
import time
import psutil
from tkinter import messagebox

def log(message):
    """Print log message with timestamp"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def kill_pycharm_processes():
    """Kill all PyCharm related processes"""
    log("Killing PyCharm processes...")
    killed_count = 0
    
    pycharm_processes = ['pycharm64.exe', 'pycharm.exe', 'idea64.exe', 'idea.exe', 'fsnotifier.exe', 'fsnotifier64.exe']
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            proc_name = proc.info['name'].lower()
            proc_exe = proc.info['exe'].lower() if proc.info['exe'] else ''
            
            if (any(pycharm_proc in proc_name for pycharm_proc in pycharm_processes) or
                'pycharm' in proc_name or 'pycharm' in proc_exe):
                try:
                    process = psutil.Process(proc.info['pid'])
                    process.terminate()
                    log(f"Terminated process: {proc.info['name']} (PID: {proc.info['pid']})")
                    killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
        except Exception:
            continue
    
    # Wait for processes to terminate
    time.sleep(2)
    
    # Force kill any remaining processes
    for proc_name in pycharm_processes:
        try:
            subprocess.run(['taskkill', '/F', '/IM', proc_name], capture_output=True, check=False)
        except Exception:
            pass
    
    log(f"Killed {killed_count} PyCharm processes")

def remove_pycharm_folders():
    """Remove PyCharm folders from all possible locations"""
    log("Removing PyCharm folders...")
    
    # Possible PyCharm installation locations
    locations = [
        r"C:\AMSSoftX\PyCharm Community Edition 2025.1.3",
        r"C:\Program Files\JetBrains\PyCharm Community Edition 2025.1.3",
        r"C:\Program Files (x86)\JetBrains\PyCharm Community Edition 2025.1.3",
        os.path.join(os.environ.get('LOCALAPPDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('APPDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('PROGRAMDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('USERPROFILE', ''), '.PyCharmCE2025.1'),
        os.path.join(os.environ.get('USERPROFILE', ''), '.cache', 'JetBrains'),
        os.path.join(os.environ.get('USERPROFILE', ''), '.config', 'JetBrains'),
        os.path.join(os.environ.get('USERPROFILE', ''), '.local', 'share', 'JetBrains'),
    ]
    
    # Search for additional PyCharm folders
    search_roots = ['C:\\', 'D:\\']
    for root in search_roots:
        if os.path.exists(root):
            try:
                for item in os.listdir(root):
                    if 'pycharm' in item.lower():
                        potential_location = os.path.join(root, item)
                        if os.path.isdir(potential_location) and potential_location not in locations:
                            locations.append(potential_location)
            except Exception:
                continue
    
    removed_count = 0
    for location in locations:
        if os.path.exists(location):
            log(f"Removing: {location}")
            try:
                # Take ownership and set permissions
                subprocess.run(["takeown", "/F", location, "/A", "/R", "/D", "Y"], 
                             capture_output=True, check=False)
                subprocess.run(["icacls", location, "/grant", "Administrators:F", "/T", "/C"], 
                             capture_output=True, check=False)
                
                # Remove the folder
                shutil.rmtree(location, ignore_errors=True)
                
                if not os.path.exists(location):
                    log(f"Successfully removed: {location}")
                    removed_count += 1
                else:
                    # Schedule for deletion on reboot
                    try:
                        import ctypes
                        MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                        res = ctypes.windll.kernel32.MoveFileExW(str(location), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                        if res != 0:
                            log(f"Scheduled for deletion on reboot: {location}")
                        else:
                            log(f"Failed to schedule for deletion: {location}")
                    except Exception:
                        log(f"Could not schedule for deletion: {location}")
                        
            except Exception as e:
                log(f"Error removing {location}: {e}")
    
    log(f"Removed {removed_count} folders")

def remove_pycharm_registry():
    """Remove PyCharm registry entries"""
    log("Removing PyCharm registry entries...")
    
    # Registry paths to clean
    registry_paths = [
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Classes"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\JetBrains"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JetBrains"),
    ]
    
    removed_count = 0
    for root_key, path in registry_paths:
        try:
            with winreg.OpenKey(root_key, path, 0, winreg.KEY_ALL_ACCESS) as key:
                subkeys_to_delete = []
                
                # Find PyCharm related subkeys
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        if ('pycharm' in subkey_name.lower() or 
                            'jetbrains' in subkey_name.lower() or
                            'intellijideaprojectfile' in subkey_name.lower()):
                            subkeys_to_delete.append(subkey_name)
                    except Exception:
                        continue
                
                # Delete the subkeys
                for subkey_name in subkeys_to_delete:
                    try:
                        winreg.DeleteKey(key, subkey_name)
                        log(f"Removed registry key: {path}\\{subkey_name}")
                        removed_count += 1
                    except Exception as e:
                        log(f"Error removing registry key {subkey_name}: {e}")
                        
        except Exception as e:
            log(f"Error accessing registry path {path}: {e}")
    
    log(f"Removed {removed_count} registry entries")

def main():
    """Main uninstaller function"""
    print("=" * 60)
    print("PyCharm Community Edition 2025.1.3 - Complete Uninstaller")
    print("=" * 60)
    
    # Ask for confirmation
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        result = messagebox.askyesno(
            "Confirm PyCharm Uninstall",
            "This will completely remove PyCharm Community Edition 2025.1.3\n"
            "including all settings, plugins, and user data.\n\n"
            "Are you sure you want to continue?"
        )
        
        root.destroy()
        
        if not result:
            log("Uninstall cancelled by user")
            return
            
    except Exception:
        response = input("Continue with PyCharm uninstall? (y/N): ")
        if response.lower() != 'y':
            log("Uninstall cancelled")
            return
    
    log("Starting PyCharm complete uninstall...")
    
    # Step 1: Kill processes
    kill_pycharm_processes()
    
    # Step 2: Remove folders
    remove_pycharm_folders()
    
    # Step 3: Remove registry entries
    remove_pycharm_registry()
    
    log("PyCharm uninstall completed!")
    log("Please restart your computer to complete the removal.")
    
    print("\n" + "=" * 60)
    print("PyCharm has been completely removed from your system.")
    print("You may need to restart your computer to complete the process.")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log("Uninstall interrupted by user")
    except Exception as e:
        log(f"Error during uninstall: {e}")
    
    input("\nPress Enter to exit...")
