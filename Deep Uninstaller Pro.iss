#define MyAppName "Deep Uninstaller Pro"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "AMSSoftX"
#define MyAppURL "https://www.amssoftx.com/"
#define MyAppExeName "Deep_Uninstaller_Pro.exe"
#define MyAppAssocExt ".myp"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
AppId={{0998E1D9-920A-48AE-91FD-217B3104B33C}} ; ⚠ MUST be unique per app - Corrected for literal curly braces
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright=Copyright © {#MyAppPublisher}
VersionInfoVersion={#MyAppVersion}
VersionInfoCompany={#MyAppPublisher}
VersionInfoDescription={#MyAppName} Setup
VersionInfoTextVersion={#MyAppVersion}
VersionInfoProductName={#MyAppName}
VersionInfoProductVersion={#MyAppVersion}
DefaultDirName={autopf}\{#MyAppPublisher}\{#MyAppName}
DefaultGroupName={#MyAppName}
OutputBaseFilename=Deep Uninstaller Pro
Compression=lzma2
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64
UninstallDisplayIcon={app}\{#MyAppExeName}
UninstallDisplayName={#MyAppName}
ChangesAssociations=yes
DisableWelcomePage=no
PrivilegesRequired=admin
SetupIconFile="C:/Users/<USER>/AMSSoftX/InstallerBuild/icon.ico"
WizardImageFile="C:/Users/<USER>/AMSSoftX/InstallerBuild/icon.bmp"
WizardSmallImageFile="C:/Users/<USER>/AMSSoftX/InstallerBuild/icon.bmp"
LicenseFile="C:/Users/<USER>/AMSSoftX/InstallerBuild/License.txt"

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "C:/Users/<USER>/AMSSoftX/InstallerBuild/Deep_Uninstaller_Pro.exe"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; You can add more files/folders here for a larger application
; Example: Source: "C:\YourApp\Data\*"; DestDir: "{app}\Data"; Flags: ignoreversion recursesubdirs createallsubdirs
; Example: Source: "C:\YourApp\DLLs\*.dll"; DestDir: "{app}"; Flags: ignoreversion


[Registry]
Root: HKCR; Subkey: "{#MyAppAssocExt}"; ValueType: string; ValueData: "{#MyAppAssocKey}"; Flags: uninsdeletekey
Root: HKCR; Subkey: "{#MyAppAssocKey}"; ValueType: string; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKCR; Subkey: "{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueData: """{app}\{#MyAppExeName}"",0"
Root: HKCR; Subkey: "{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueData: """{app}\{#MyAppExeName}"" ""%1"""

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "Launch {#MyAppName}"; Flags: nowait postinstall skipifsilent runasoriginaluser

[Code]
var
    LicensePage: TInputQueryWizardPage;

function CheckLicenseKey(Key: string): Boolean;
begin
    Result := Key = 'AMSSoftX-3LRX-6PRV-IUIU-3L0N';
end;

procedure InitializeWizard;
begin
    LicensePage := CreateInputQueryPage(wpWelcome,
        'License Key Verification',
        'Welcome to the official installer of AMSSoftX.',
        'This software is licensed to AMSSoftX.' + #13#10 +
        'Visit us at https://www.amssoftx.com/ <NAME_EMAIL> for support.' + #13#10#13#10 +
        'How to Buy License : Mail On : <EMAIL> | Call & Meassage : +91 **********.' + #13#10#13#10 +
        'Please enter your license key below to proceed with installation.');

    LicensePage.Add('License Key:', False);
end;

function NextButtonClick(CurPageID: Integer): Boolean;
begin
    Result := True;

    if CurPageID = LicensePage.ID then
    begin
        if not CheckLicenseKey(LicensePage.Values[0]) then
        begin
            MsgBox('Invalid license key. Please contact <NAME_EMAIL>.', mbError, MB_OK);
            Result := False;
        end;
    end;
end;
