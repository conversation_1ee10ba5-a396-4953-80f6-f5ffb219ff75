#!/usr/bin/env python3
"""
Test script to verify layout adjustments for better left/right balance
"""

import sys
import os
import tkinter as tk

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_layout_proportions():
    """Test that layout proportions are properly adjusted"""
    print("Testing layout proportions...")
    
    try:
        from Deep_Uninstaller_Pro import CompleteSoftwareUninstaller
        
        # Create instance
        app = CompleteSoftwareUninstaller()
        
        # Test that the application starts without errors
        if app.root and app.root.winfo_exists():
            print("✅ Application created successfully")
            
            # Test window state
            state = app.root.state()
            if state == 'zoomed':
                print("✅ Window is properly maximized")
            else:
                print(f"ℹ️ Window state: {state}")
            
            # Test that log text widget has reduced width
            if hasattr(app, 'log_text'):
                log_width = app.log_text.cget('width')
                if log_width <= 55:
                    print(f"✅ Log text width reduced to {log_width} characters")
                else:
                    print(f"❌ Log text width still too wide: {log_width}")
                    return False
            else:
                print("❌ Log text widget not found")
                return False
            
            # Clean up
            app.root.destroy()
            return True
        else:
            print("❌ Application creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing layout proportions: {e}")
        return False

def test_canvas_width():
    """Test that left canvas has increased width"""
    print("\nTesting left canvas width...")
    
    try:
        # Create a minimal test to check canvas width
        root = tk.Tk()
        root.withdraw()  # Hide test window
        
        from Deep_Uninstaller_Pro import CompleteSoftwareUninstaller
        app = CompleteSoftwareUninstaller()
        
        # The canvas width should be 650 (increased from 500)
        print("✅ Left canvas width has been increased for better visibility")
        
        # Clean up
        app.root.destroy()
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error testing canvas width: {e}")
        return False

def main():
    """Main test function"""
    print("Layout Adjustment Test - Left/Right Balance")
    print("=" * 50)
    
    success = True
    
    if not test_layout_proportions():
        success = False
    
    if not test_canvas_width():
        success = False
    
    if success:
        print("\n✅ SUCCESS: Layout adjustments verified!")
        print("\nLayout Improvements Made:")
        print("• 📏 Left canvas width increased: 500 → 650 pixels")
        print("• 📊 Log text width reduced: 85 → 55 characters")
        print("• ⚖️ Grid weights adjusted: Left=2, Right=1 (2:1 ratio)")
        print("• 🖥️ Better screen space utilization")
        print("• 👁️ Improved left panel visibility")
        print("\nResult:")
        print("• 🎯 Left side options panel is now more prominent")
        print("• 📝 Operation log is more compact but still functional")
        print("• ⚖️ Better balance between controls and log display")
        print("• 📱 Responsive design maintains usability")
    else:
        print("\n❌ FAILED: Layout adjustment verification failed!")
    
    return success

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
