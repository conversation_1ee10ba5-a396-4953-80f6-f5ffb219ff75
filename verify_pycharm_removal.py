#!/usr/bin/env python3
"""
PyCharm Removal Verification Script
This script checks if PyCharm has been completely removed from the system
"""

import os
import winreg
import time

def log(message):
    """Print log message with timestamp"""
    timestamp = time.strftime("%H:%M:%S")
    print(f"[{timestamp}] {message}")

def check_folders():
    """Check if PyCharm folders still exist"""
    log("Checking for remaining PyCharm folders...")
    
    locations = [
        r"C:\AMSSoftX\PyCharm Community Edition 2025.1.3",
        r"C:\Program Files\JetBrains\PyCharm Community Edition 2025.1.3",
        r"C:\Program Files (x86)\JetBrains\PyCharm Community Edition 2025.1.3",
        os.path.join(os.environ.get('LOCALAPPDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('APPDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('PROGRAMDATA', ''), 'JetBrains'),
        os.path.join(os.environ.get('USERPROFILE', ''), '.PyCharmCE2025.1'),
    ]
    
    found_folders = []
    for location in locations:
        if os.path.exists(location):
            found_folders.append(location)
            log(f"❌ Found remaining folder: {location}")
    
    if not found_folders:
        log("✅ No PyCharm folders found - Clean!")
    
    return len(found_folders) == 0

def check_registry():
    """Check if PyCharm registry entries still exist"""
    log("Checking for remaining PyCharm registry entries...")
    
    registry_paths = [
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
        (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Classes"),
        (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Classes"),
    ]
    
    found_entries = []
    for root_key, path in registry_paths:
        try:
            with winreg.OpenKey(root_key, path) as key:
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        if ('pycharm' in subkey_name.lower() or 
                            'jetbrains' in subkey_name.lower() or
                            'intellijideaprojectfile' in subkey_name.lower()):
                            found_entries.append(f"{path}\\{subkey_name}")
                            log(f"❌ Found remaining registry entry: {path}\\{subkey_name}")
                    except Exception:
                        continue
        except Exception:
            continue
    
    if not found_entries:
        log("✅ No PyCharm registry entries found - Clean!")
    
    return len(found_entries) == 0

def check_installed_software():
    """Check if PyCharm appears in the installed software list"""
    log("Checking installed software list...")
    
    registry_paths = [
        r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
        r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
    ]
    
    found_software = []
    for path in registry_paths:
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        with winreg.OpenKey(key, subkey_name) as subkey:
                            try:
                                display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                if 'pycharm' in display_name.lower():
                                    found_software.append(display_name)
                                    log(f"❌ Found in installed software: {display_name}")
                            except FileNotFoundError:
                                pass
                    except Exception:
                        continue
        except Exception:
            continue
    
    if not found_software:
        log("✅ PyCharm not found in installed software list - Clean!")
    
    return len(found_software) == 0

def main():
    """Main verification function"""
    print("=" * 60)
    print("PyCharm Removal Verification")
    print("=" * 60)
    
    folders_clean = check_folders()
    print()
    
    registry_clean = check_registry()
    print()
    
    software_clean = check_installed_software()
    print()
    
    print("=" * 60)
    if folders_clean and registry_clean and software_clean:
        print("🎉 SUCCESS: PyCharm has been completely removed!")
        print("✅ All folders removed")
        print("✅ All registry entries removed")
        print("✅ Removed from installed software list")
    else:
        print("⚠️  WARNING: PyCharm removal incomplete")
        if not folders_clean:
            print("❌ Some folders still exist")
        if not registry_clean:
            print("❌ Some registry entries still exist")
        if not software_clean:
            print("❌ Still appears in installed software")
        print("\nYou may need to:")
        print("1. Run the uninstaller as administrator")
        print("2. Restart your computer")
        print("3. Manually remove remaining items")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        log(f"Error during verification: {e}")
    
    input("\nPress Enter to exit...")
