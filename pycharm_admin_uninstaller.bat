@echo off
title PyCharm Complete Uninstaller - Admin Mode

:: Check for administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

echo.
echo ============================================================
echo   PyCharm Community Edition 2025.1.3 - Complete Uninstaller
echo ============================================================
echo.

:: Kill PyCharm processes
echo Killing PyCharm processes...
taskkill /F /IM "pycharm-community-2025.1.3.exe" >nul 2>&1
taskkill /F /IM "pycharm64.exe" >nul 2>&1
taskkill /F /IM "pycharm.exe" >nul 2>&1
taskkill /F /IM "idea64.exe" >nul 2>&1
taskkill /F /IM "idea.exe" >nul 2>&1
taskkill /F /IM "fsnotifier.exe" >nul 2>&1
taskkill /F /IM "fsnotifier64.exe" >nul 2>&1
echo PyCharm processes terminated.

:: Remove folders
echo.
echo Removing PyCharm folders...

if exist "C:\AMSSoftX\PyCharm Community Edition 2025.1.3" (
    echo Removing main installation folder...
    takeown /F "C:\AMSSoftX\PyCharm Community Edition 2025.1.3" /A /R /D Y >nul 2>&1
    icacls "C:\AMSSoftX\PyCharm Community Edition 2025.1.3" /grant Administrators:F /T /C >nul 2>&1
    rmdir /S /Q "C:\AMSSoftX\PyCharm Community Edition 2025.1.3" >nul 2>&1
    if not exist "C:\AMSSoftX\PyCharm Community Edition 2025.1.3" (
        echo Successfully removed main installation folder.
    ) else (
        echo Warning: Main installation folder still exists.
    )
)

if exist "%LOCALAPPDATA%\JetBrains" (
    echo Removing user data folder...
    rmdir /S /Q "%LOCALAPPDATA%\JetBrains" >nul 2>&1
)

if exist "%APPDATA%\JetBrains" (
    echo Removing roaming data folder...
    rmdir /S /Q "%APPDATA%\JetBrains" >nul 2>&1
)

if exist "%PROGRAMDATA%\JetBrains" (
    echo Removing program data folder...
    rmdir /S /Q "%PROGRAMDATA%\JetBrains" >nul 2>&1
)

if exist "%USERPROFILE%\.PyCharmCE2025.1" (
    echo Removing user config folder...
    rmdir /S /Q "%USERPROFILE%\.PyCharmCE2025.1" >nul 2>&1
)

:: Remove registry entries
echo.
echo Removing registry entries...

:: Remove uninstall entries
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\PyCharm Community Edition 2025.1.3" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\PyCharm Community Edition 2025.1.3" /f >nul 2>&1

:: Remove file associations
reg delete "HKLM\SOFTWARE\Classes\IntelliJIdeaProjectFile" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Classes\PyCharmCE2025.1" /f >nul 2>&1

:: Remove context menu entries
reg delete "HKLM\SOFTWARE\Classes\*\shell\Open with PyCharm Community Edition" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Classes\Directory\shell\PyCharm Community Edition" /f >nul 2>&1
reg delete "HKLM\SOFTWARE\Classes\Directory\Background\shell\PyCharm Community Edition" /f >nul 2>&1

:: Remove JetBrains entries
reg delete "HKLM\SOFTWARE\JetBrains" /f >nul 2>&1
reg delete "HKCU\SOFTWARE\JetBrains" /f >nul 2>&1

echo Registry entries removed.

:: Clean up shortcuts
echo.
echo Removing shortcuts...
del "%PUBLIC%\Desktop\PyCharm Community Edition 2025.1.3.lnk" >nul 2>&1
del "%USERPROFILE%\Desktop\PyCharm Community Edition 2025.1.3.lnk" >nul 2>&1
del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\JetBrains\PyCharm Community Edition 2025.1.3.lnk" >nul 2>&1
rmdir /S /Q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\JetBrains" >nul 2>&1

echo.
echo ============================================================
echo PyCharm Community Edition 2025.1.3 has been completely removed!
echo ============================================================
echo.
echo Please restart your computer to complete the removal process.
echo.
pause
