import os
import sys
import shutil
import subprocess
import winreg
import psutil
import sqlite3
import json
import time
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk
import threading

class CompleteSoftwareUninstaller:
    def __init__(self):
        self.log_messages = []  # Initialize log storage first
        self.root = tk.Tk()
        self.root.title("Complete Software Uninstaller (AMSSoftX - Deep Uninstaller)")
        # Open in full screen
        self.root.state('zoomed')  # For Windows full screen
        # Try to use a modern theme
        style = ttk.Style()
        for theme in ("vista", "clam", "alt", "default"):
            if theme in style.theme_names():
                style.theme_use(theme)
                break
        # Set a modern color scheme
        self.root.configure(bg="#f4f6fb")
        style.configure("TFrame", background="#f4f6fb")
        style.configure("TLabel", background="#f4f6fb", font=("Segoe UI", 11))
        style.configure("TButton", font=("Segoe UI", 11, "bold"), padding=6)
        style.configure("TCheckbutton", background="#f4f6fb", font=("Segoe UI", 10))
        style.configure("TLabelframe", background="#e9edf5", font=("Segoe UI", 11, "bold"))
        style.configure("TLabelframe.Label", background="#e9edf5", font=("Segoe UI", 12, "bold"))
        
        # Main frame with two columns
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Left frame for controls
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.N, tk.W, tk.S), padx=(0, 20))
        
        # Right frame for log
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.N, tk.E, tk.W, tk.S))
        
        # Large title
        title_label = ttk.Label(left_frame, text="AMSSoftX - Deep Uninstaller", font=("Segoe UI", 24, "bold"), foreground="#2a3b8f")
        title_label.grid(row=0, column=0, pady=(0, 20), sticky=tk.W)
        
        # Software selection
        ttk.Label(left_frame, text="Select Software to Uninstall:", font=("Segoe UI", 12, "bold"), foreground="#2a3b8f").grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.software_var = tk.StringVar()
        self.software_combo = ttk.Combobox(left_frame, textvariable=self.software_var, width=50, font=("Segoe UI", 11))
        self.software_combo.grid(row=2, column=0, pady=5, sticky=(tk.W, tk.E))
        
        # Refresh button
        ttk.Button(left_frame, text="Refresh List", command=self.refresh_software_list).grid(row=2, column=1, padx=5)
        
        # Options frame
        options_frame = ttk.LabelFrame(left_frame, text="Uninstall Options", padding="10 10 10 10")
        options_frame.grid(row=3, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        self.remove_files = tk.BooleanVar(value=True)
        self.remove_registry = tk.BooleanVar(value=True)
        self.remove_services = tk.BooleanVar(value=True)
        self.remove_tasks = tk.BooleanVar(value=True)
        self.remove_db = tk.BooleanVar(value=True)
        self.remove_uninstallers = tk.BooleanVar(value=True)
        # Advanced uninstall options
        self.remove_api_hooks = tk.BooleanVar(value=True)
        self.remove_dlls = tk.BooleanVar(value=True)
        self.remove_com_objects = tk.BooleanVar(value=True)
        self.remove_file_associations = tk.BooleanVar(value=True)
        self.remove_context_menus = tk.BooleanVar(value=True)
        self.remove_firewall_rules = tk.BooleanVar(value=True)
        self.remove_network_connections = tk.BooleanVar(value=True)
        self.remove_startup_entries = tk.BooleanVar(value=True)
        self.remove_system_integration = tk.BooleanVar(value=True)

        ttk.Checkbutton(options_frame, text="Remove Files & Folders", variable=self.remove_files).grid(row=0, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove Registry Entries", variable=self.remove_registry).grid(row=0, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Stop & Remove Services", variable=self.remove_services).grid(row=1, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove Scheduled Tasks", variable=self.remove_tasks).grid(row=1, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Clean Database Entries", variable=self.remove_db).grid(row=2, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove leftover uninstallers (unins*.exe)", variable=self.remove_uninstallers).grid(row=2, column=1, sticky=tk.W)
        # Advanced options
        ttk.Checkbutton(options_frame, text="Remove API hooks", variable=self.remove_api_hooks).grid(row=3, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove DLL registrations", variable=self.remove_dlls).grid(row=3, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove COM objects", variable=self.remove_com_objects).grid(row=4, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove file associations", variable=self.remove_file_associations).grid(row=4, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove context menu entries", variable=self.remove_context_menus).grid(row=5, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove firewall rules", variable=self.remove_firewall_rules).grid(row=5, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove network connections", variable=self.remove_network_connections).grid(row=6, column=0, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove startup entries", variable=self.remove_startup_entries).grid(row=6, column=1, sticky=tk.W)
        ttk.Checkbutton(options_frame, text="Remove system integration", variable=self.remove_system_integration).grid(row=7, column=0, sticky=tk.W)
        
        # Action buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=15)
        
        ttk.Button(button_frame, text="Scan Software", command=self.scan_software, width=18).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="Complete Uninstall", command=self.complete_uninstall, width=18).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="Clear Log", command=self.clear_log, width=18).pack(side=tk.LEFT, padx=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(left_frame, mode='indeterminate', length=400)
        self.progress.grid(row=5, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        
        # Log area in right frame
        ttk.Label(right_frame, text="Uninstall Log:", font=("Segoe UI", 12, "bold"), foreground="#2a3b8f").grid(row=0, column=0, sticky=tk.W, pady=(0,5))
        
        self.log_text = scrolledtext.ScrolledText(right_frame, width=80, height=40, font=("Consolas", 10), bg="#f8fafc", wrap=tk.NONE)
        self.log_text.grid(row=1, column=0, sticky=(tk.N, tk.S, tk.E, tk.W))
        
        # Add horizontal scrollbar
        h_scroll = tk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.log_text.xview)
        h_scroll.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.log_text.config(xscrollcommand=h_scroll.set)
        
        # Developer info
        dev_frame = ttk.Frame(left_frame)
        dev_frame.grid(row=6, column=0, columnspan=2, pady=(10,0))
        ttk.Label(dev_frame, text="Developed By AMSSoftX", font=("Segoe UI", 10)).pack(side=tk.LEFT, padx=5)
        website_label = ttk.Label(dev_frame, text="Website: https://amssoftx.com", font=("Segoe UI", 10), foreground="blue", cursor="hand2")
        website_label.pack(side=tk.LEFT, padx=5)
        website_label.bind("<Button-1>", lambda e: webbrowser.open("https://amssoftx.com"))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # Initial software list load
        self.refresh_software_list()
    
    def log(self, message):
        """Add message to log"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        self.log_messages.append(full_message)
        
        # Update UI
        self.log_text.insert(tk.END, full_message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def refresh_software_list(self):
        """Refresh the list of installed software"""
        self.log("Refreshing software list...")
        software_list = self.get_installed_software()
        self.software_combo['values'] = software_list
        self.log(f"Found {len(software_list)} installed programs")
    
    def get_installed_software(self):
        """Get list of installed software from registry"""
        software_list = []
        
        # Registry paths to check
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if display_name and display_name not in software_list:
                                        software_list.append(display_name)
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue
            except Exception as e:
                self.log(f"Error reading registry: {e}")
        
        return sorted(software_list)
    
    def scan_software(self):
        """Scan selected software for all components"""
        software_name = self.software_var.get()
        if not software_name:
            messagebox.showwarning("Warning", "Please select a software to scan")
            return
        
        self.log(f"Scanning {software_name}...")
        
        # Start scanning in separate thread
        thread = threading.Thread(target=self._scan_software_thread, args=(software_name,))
        thread.daemon = True
        thread.start()
    
    def _scan_software_thread(self, software_name):
        """Scan software in separate thread"""
        self.progress.start()
        
        try:
            # Find software info
            software_info = self.find_software_info(software_name)
            
            if software_info:
                self.log(f"Found software: {software_name}")
                self.log(f"Install Location: {software_info.get('InstallLocation', 'Not found')}")
                self.log(f"Uninstall String: {software_info.get('UninstallString', 'Not found')}")
                
                # Scan for files
                self.scan_files(software_name, software_info)
                
                # Scan for registry entries
                self.scan_registry_entries(software_name)
                
                # Scan for services
                self.scan_services(software_name)
                
                # Scan for scheduled tasks
                self.scan_scheduled_tasks(software_name)
                
                self.log(f"Scan completed for {software_name}")
            else:
                self.log(f"Software {software_name} not found in registry")
                
        except Exception as e:
            self.log(f"Error during scan: {e}")
        finally:
            self.progress.stop()
    
    def find_software_info(self, software_name):
        """Find software information in registry"""
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if display_name == software_name:
                                        # Get all values
                                        software_info = {}
                                        for j in range(winreg.QueryInfoKey(subkey)[1]):
                                            try:
                                                value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                                software_info[value_name] = value_data
                                            except Exception:
                                                continue
                                        return software_info
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue
            except Exception:
                continue
        return None
    
    def scan_files(self, software_name, software_info):
        """Scan for software files and folders"""
        self.log("Scanning for files and folders...")
        
        # Common locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        
        # Add install location if available
        if 'InstallLocation' in software_info:
            locations.append(software_info['InstallLocation'])
        
        found_paths = []
        for location in locations:
            if os.path.exists(location):
                found_paths.append(location)
                self.log(f"Found folder: {location}")
        
        # Scan for files in common locations
        common_files = [
            os.path.join(os.environ.get('TEMP', ''), f"{software_name}*"),
            os.path.join(os.environ.get('WINDIR', ''), 'Temp', f"{software_name}*"),
        ]
        
        return found_paths
    
    def scan_registry_entries(self, software_name):
        """Scan for registry entries"""
        self.log("Scanning for registry entries...")
        
        # Registry roots to check
        registry_roots = [
            (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE"),
            (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
            (winreg.HKEY_USERS, "HKEY_USERS"),
        ]
        
        found_entries = []
        for root, root_name in registry_roots:
            try:
                self._scan_registry_recursive(root, "", software_name, found_entries, root_name)
            except Exception as e:
                self.log(f"Error scanning {root_name}: {e}")
        
        self.log(f"Found {len(found_entries)} registry entries")
        return found_entries
    
    def _scan_registry_recursive(self, key, path, software_name, found_entries, root_name, max_depth=3):
        """Recursively scan registry for software entries"""
        if max_depth <= 0:
            return
        
        try:
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    if software_name.lower() in subkey_name.lower():
                        full_path = f"{root_name}\\{path}\\{subkey_name}" if path else f"{root_name}\\{subkey_name}"
                        found_entries.append(full_path)
                        self.log(f"Found registry key: {full_path}")
                    
                    # Recurse into subkey
                    try:
                        with winreg.OpenKey(key, subkey_name) as subkey:
                            new_path = f"{path}\\{subkey_name}" if path else subkey_name
                            self._scan_registry_recursive(subkey, new_path, software_name, found_entries, root_name, max_depth-1)
                    except Exception:
                        pass
                except Exception:
                    continue
        except Exception:
            pass
    
    def scan_services(self, software_name):
        """Scan for related services"""
        self.log("Scanning for services...")
        
        found_services = []
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    found_services.append(service_info)
                    self.log(f"Found service: {service_info['name']} ({service_info['display_name']})")
            except Exception:
                continue
        
        return found_services
    
    def scan_scheduled_tasks(self, software_name):
        """Scan for scheduled tasks"""
        self.log("Scanning for scheduled tasks...")
        
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                found_tasks = []
                for line in lines[1:]:  # Skip header
                    if software_name.lower() in line.lower():
                        found_tasks.append(line)
                        self.log(f"Found scheduled task: {line}")
                return found_tasks
        except Exception as e:
            self.log(f"Error scanning scheduled tasks: {e}")
        
        return []
    
    def complete_uninstall(self):
        """Perform complete uninstall"""
        software_name = self.software_var.get()
        if not software_name:
            messagebox.showwarning("Warning", "Please select a software to uninstall")
            return
        
        # Confirmation dialog
        if not messagebox.askyesno("Confirm Uninstall", 
                                  f"Are you sure you want to completely uninstall {software_name}?\n\n"
                                  "This will remove ALL traces of the software including:\n"
                                  "- Files and folders\n"
                                  "- Registry entries\n"
                                  "- Services\n"
                                  "- Scheduled tasks\n"
                                  "- Database entries\n\n"
                                  "This action cannot be undone!"):
            return
        
        # Start uninstall in separate thread
        thread = threading.Thread(target=self._complete_uninstall_thread, args=(software_name,))
        thread.daemon = True
        thread.start()
    
    def _complete_uninstall_thread(self, software_name):
        """Complete uninstall in separate thread"""
        self.progress.start()
        try:
            self.log(f"Starting complete uninstall of {software_name}...")
            software_info = self.find_software_info(software_name)
            if not software_info:
                self.log(f"Software {software_name} not found in registry")
                return
            self.stop_related_processes(software_name)
            if self.remove_services.get():
                self.remove_software_services(software_name)
            if self.remove_tasks.get():
                self.remove_software_tasks(software_name)
            if 'UninstallString' in software_info:
                self.run_official_uninstaller(software_info['UninstallString'])
            if self.remove_files.get():
                self.remove_software_files(software_name, software_info)
            if self.remove_registry.get():
                self.remove_software_registry(software_name)
            if self.remove_db.get():
                self.clean_database_entries(software_name)
            # Advanced checklist options
            if self.remove_api_hooks.get():
                self.clean_windows_api_hooks(software_name)
            if self.remove_dlls.get():
                self.clean_dll_registrations(software_name)
            if self.remove_com_objects.get():
                self.clean_com_objects(software_name)
            if self.remove_file_associations.get():
                self.clean_file_associations(software_name)
            if self.remove_context_menus.get():
                self.clean_context_menu_entries(software_name)
            if self.remove_firewall_rules.get():
                self.clean_firewall_rules(software_name)
            if self.remove_network_connections.get():
                self.clean_network_connections(software_name)
            if self.remove_startup_entries.get():
                self.clean_startup_entries(software_name)
            if self.remove_system_integration.get():
                self.clean_system_integration(software_name)
            self.log(f"Deep clean of API, system integration, COM, DLLs, etc. completed for {software_name}")
            self.verify_removal(software_name)
            if self.remove_uninstallers.get():
                self.remove_leftover_uninstallers(software_name, software_info)
            self.log(f"Complete uninstall of {software_name} finished!")
            messagebox.showinfo("Success", f"{software_name} has been completely uninstalled!")
        except Exception as e:
            self.log(f"Error during uninstall: {e}")
            messagebox.showerror("Error", f"Error during uninstall: {e}")
        finally:
            self.progress.stop()
    
    def verify_removal(self, software_name):
        """Verify that all traces of the software are removed"""
        self.log(f"Verifying removal of {software_name}...")
        # Check for remaining files/folders
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        for location in locations:
            if os.path.exists(location):
                self.log(f"Warning: Folder still exists: {location}")
        # Check for running processes
        for proc in psutil.process_iter(['name', 'exe', 'cmdline']):
            try:
                if software_name.lower() in proc.info['name'].lower():
                    self.log(f"Warning: Process still running: {proc.info['name']}")
            except Exception:
                continue
        # Check for registry entries
        found_entries = self.scan_registry_entries(software_name)
        if found_entries:
            self.log(f"Warning: Registry entries still found: {len(found_entries)}")
        # Check for services
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    self.log(f"Warning: Service still exists: {service_info['name']}")
            except Exception:
                continue
        # Check for scheduled tasks
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:
                    if software_name.lower() in line.lower():
                        self.log(f"Warning: Scheduled task still exists: {line}")
        except Exception:
            pass
        self.log(f"Verification complete for {software_name}.")

    def stop_related_processes(self, software_name):
        """Stop all processes related to the software - Advanced Task Manager Kill"""
        self.log("Stopping related processes from Task Manager...")
        
        stopped_processes = []
        force_killed = []
        
        # Phase 1: Find ALL related processes (including child processes)
        target_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'cwd']):
            try:
                # Check process name
                if software_name.lower() in proc.info['name'].lower():
                    target_processes.append(proc)
                    continue
                
                # Check executable path
                if proc.info['exe'] and software_name.lower() in proc.info['exe'].lower():
                    target_processes.append(proc)
                    continue
                
                # Check command line arguments
                if proc.info['cmdline']:
                    cmdline_str = ' '.join(proc.info['cmdline']).lower()
                    if software_name.lower() in cmdline_str:
                        target_processes.append(proc)
                        continue
                
                # Check working directory
                if proc.info['cwd'] and software_name.lower() in proc.info['cwd'].lower():
                    target_processes.append(proc)
                    continue
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, TypeError):
                continue
        
        self.log(f"Found {len(target_processes)} related processes")
        
        # Phase 2: Stop child processes first
        for proc in target_processes:
            try:
                # Get all children
                children = proc.children(recursive=True)
                for child in children:
                    try:
                        child.terminate()
                        self.log(f"Terminated child process: {child.name()} (PID: {child.pid})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
                # Terminate main process
                proc.terminate()
                stopped_processes.append(proc.info['name'])
                self.log(f"Terminated process: {proc.info['name']} (PID: {proc.info['pid']})")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Wait for graceful termination
        time.sleep(3)
        
        # Phase 3: Force kill remaining processes
        for proc in target_processes:
            try:
                if proc.is_running():
                    # Try using Windows taskkill command for stubborn processes
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', str(proc.pid)], 
                                     capture_output=True, check=False)
                        self.log(f"Force killed with taskkill: {proc.name()} (PID: {proc.pid})")
                    except:
                        pass
                    
                    # Final attempt with psutil
                    try:
                        proc.kill()
                        force_killed.append(proc.name())
                        self.log(f"Force killed process: {proc.name()} (PID: {proc.pid})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Phase 4: Kill by process name using taskkill (catch any remaining)
        try:
            subprocess.run(['taskkill', '/F', '/IM', f"{software_name}*"], 
                         capture_output=True, check=False)
            self.log(f"Executed taskkill for all {software_name} processes")
        except Exception:
            pass
        
        # Phase 5: Advanced process cleanup
        self.advanced_process_cleanup(software_name)
        
        self.log(f"Process cleanup completed: {len(stopped_processes)} terminated, {len(force_killed)} force killed")
    
    def advanced_process_cleanup(self, software_name):
        """Advanced process cleanup including handles and memory"""
        self.log("Performing advanced process cleanup...")
        
        try:
            # Kill processes by window title
            import win32gui
            import win32process
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_title = win32gui.GetWindowText(hwnd)
                    if software_name.lower() in window_title.lower():
                        try:
                            pid = win32process.GetWindowThreadProcessId(hwnd)[1]
                            process = psutil.Process(pid)
                            if process.is_running():
                                process.terminate()
                                windows.append(f"Terminated process by window title: {window_title} (PID: {pid})")
                        except Exception:
                            pass
            # Enum windows and terminate processes
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            for message in windows:
                self.log(message)
        except Exception as e:
            self.log(f"Error in advanced process cleanup: {e}")
    
    def clean_windows_api_hooks(self, software_name):
        self.log(f"[Advanced] Cleaning Windows API hooks for {software_name} (TODO)")
        # TODO: Implement actual API hook removal
    
    def clean_dll_registrations(self, software_name):
        self.log(f"[Advanced] Cleaning DLL registrations for {software_name} (TODO)")
        # TODO: Implement actual DLL unregistration
    
    def clean_com_objects(self, software_name):
        self.log(f"[Advanced] Cleaning COM objects for {software_name} (TODO)")
        # TODO: Implement actual COM object removal
    
    def clean_file_associations(self, software_name):
        self.log(f"[Advanced] Cleaning file associations for {software_name} (TODO)")
        # TODO: Implement actual file association removal
    
    def clean_context_menu_entries(self, software_name):
        self.log(f"[Advanced] Cleaning context menu entries for {software_name} (TODO)")
        # TODO: Implement actual context menu entry removal
    
    def clean_firewall_rules(self, software_name):
        self.log(f"[Advanced] Cleaning firewall rules for {software_name} (TODO)")
        # TODO: Implement actual firewall rule removal
    
    def clean_network_connections(self, software_name):
        self.log(f"[Advanced] Cleaning network connections for {software_name} (TODO)")
        # TODO: Implement actual network connection removal
    
    def clean_startup_entries(self, software_name):
        self.log(f"[Advanced] Cleaning startup entries for {software_name} (TODO)")
        # TODO: Implement actual startup entry removal
    
    def clean_system_integration(self, software_name):
        self.log(f"[Advanced] Cleaning system integration for {software_name} (TODO)")
        # TODO: Implement actual system integration removal
    
    def remove_software_services(self, software_name):
        """Stop and remove software-related services"""
        self.log("Stopping and removing services...")
        
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    # Stop service
                    try:
                        subprocess.run(['sc', 'stop', service_info['name']], 
                                     capture_output=True, check=False)
                        self.log(f"Stopped service: {service_info['name']}")
                    except Exception:
                        pass
                    # Delete service
                    try:
                        subprocess.run(['sc', 'delete', service_info['name']], 
                                     capture_output=True, check=False)
                        self.log(f"Deleted service: {service_info['name']}")
                    except Exception as e:
                        self.log(f"Error deleting service {service_info['name']}: {e}")
            except Exception:
                pass
    
    def remove_software_tasks(self, software_name):
        """Remove software-related scheduled tasks"""
        self.log("Removing scheduled tasks...")
        
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # Skip header
                    if software_name.lower() in line.lower():
                        # Extract task name (first column)
                        task_name = line.split(',')[0].strip('"')
                        try:
                            subprocess.run(['schtasks', '/delete', '/tn', task_name, '/f'], 
                                         capture_output=True, check=False)
                            self.log(f"Deleted scheduled task: {task_name}")
                        except Exception as e:
                            self.log(f"Error deleting task {task_name}: {e}")
        except Exception as e:
            self.log(f"Error removing scheduled tasks: {e}")
    
    def run_official_uninstaller(self, uninstall_string):
        """Run the official uninstaller for the software"""
        self.log("Running official uninstaller...")
        
        try:
            # Try to run uninstaller silently
            if '/S' not in uninstall_string and '/silent' not in uninstall_string:
                uninstall_string += ' /S'
            result = subprocess.run(uninstall_string, shell=True, capture_output=True, 
                                  text=True, timeout=300)
            if result.returncode == 0:
                self.log("Official uninstaller completed successfully")
            else:
                self.log(f"Official uninstaller returned code: {result.returncode}")
        except subprocess.TimeoutExpired:
            self.log("Official uninstaller timed out")
        except Exception as e:
            self.log(f"Error running official uninstaller: {e}")
    
    def force_delete_file(self, file_path):
        """Force delete a file by killing processes, changing permissions, and removing attributes. If still fails, schedule for deletion on reboot."""
        if not os.path.exists(file_path):
            self.log(f"OK: File already deleted: {file_path}")
            return True
        # Try to kill any process using the file
        try:
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    flist = proc.open_files()
                    for f in flist:
                        if os.path.abspath(f.path) == os.path.abspath(file_path):
                            proc.kill()
                            self.log(f"Killed process {proc.name()} (PID: {proc.pid}) using {file_path}")
                except Exception:
                    continue
        except Exception:
            pass
        # Remove read-only, hidden, system attributes (Windows)
        try:
            import ctypes
            FILE_ATTRIBUTE_NORMAL = 0x80
            ctypes.windll.kernel32.SetFileAttributesW(str(file_path), FILE_ATTRIBUTE_NORMAL)
        except Exception:
            pass
        # Take ownership and set permissions
        try:
            import subprocess
            subprocess.run(["takeown", "/F", file_path, "/A", "/R", "/D", "Y"], capture_output=True, check=False)
            subprocess.run(["icacls", file_path, "/grant", "Administrators:F", "/T", "/C"], capture_output=True, check=False)
        except Exception:
            pass
        # Change permissions to allow deletion
        try:
            os.chmod(file_path, 0o777)
        except Exception:
            pass
        # Try to delete
        try:
            os.remove(file_path)
            self.log(f"Force deleted file: {file_path}")
            return True
        except FileNotFoundError:
            self.log(f"OK: File already deleted: {file_path}")
            return True
        except Exception as e:
            # Schedule for deletion on reboot
            try:
                import ctypes
                MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                res = ctypes.windll.kernel32.MoveFileExW(str(file_path), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                if res != 0:
                    self.log(f"OK: Scheduled for deletion on reboot: {file_path}")
                    return True
                else:
                    self.log(f"Failed to force delete file and schedule for deletion: {file_path}: {e}")
                    return False
            except Exception as e2:
                self.log(f"Failed to force delete file and schedule for deletion: {file_path}: {e2}")
                return False

    def _force_remove_error(self, func, path, exc_info):
        """Error handler for shutil.rmtree to force delete files/folders, or schedule for deletion on reboot."""
        try:
            # Try to delete as file or folder
            if os.path.isfile(path) or os.path.islink(path):
                self.force_delete_file(path)
            elif os.path.isdir(path):
                # Try to take ownership and set permissions
                try:
                    import subprocess
                    subprocess.run(["takeown", "/F", path, "/A", "/R", "/D", "Y"], capture_output=True, check=False)
                    subprocess.run(["icacls", path, "/grant", "Administrators:F", "/T", "/C"], capture_output=True, check=False)
                except Exception:
                    pass
                # Try to delete
                try:
                    shutil.rmtree(path)
                    self.log(f"Force deleted folder: {path}")
                except Exception:
                    # Schedule for deletion on reboot
                    try:
                        import ctypes
                        MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                        res = ctypes.windll.kernel32.MoveFileExW(str(path), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                        if res != 0:
                            self.log(f"OK: Scheduled folder for deletion on reboot: {path}")
                        else:
                            self.log(f"Failed to force delete folder and schedule for deletion: {path}")
                    except Exception as e2:
                        self.log(f"Failed to force delete folder and schedule for deletion: {path}: {e2}")
        except Exception as e:
            self.log(f"Force remove error handler failed for {path}: {e}")

    def remove_software_files(self, software_name, software_info):
        """Remove software-related files and folders"""
        self.log("Removing software files and folders...")
        # Common locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        # Add install location if available
        if 'InstallLocation' in software_info:
            locations.append(software_info['InstallLocation'])
        # Remove uninstallers first
        for location in locations:
            if os.path.exists(location):
                for file in os.listdir(location):
                    if file.lower().startswith('unins') and file.lower().endswith('.exe'):
                        file_path = os.path.join(location, file)
                        if not self.force_delete_file(file_path):
                            if os.path.exists(file_path):
                                self.log(f"Could not delete {file_path} even after force attempts.")
        # Remove folders
        for location in locations:
            if os.path.exists(location):
                try:
                    shutil.rmtree(location, onerror=self._force_remove_error)
                    self.log(f"Removed folder: {location}")
                except Exception as e:
                    # Schedule for deletion on reboot
                    try:
                        import ctypes
                        MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                        res = ctypes.windll.kernel32.MoveFileExW(str(location), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                        if res != 0:
                            self.log(f"OK: Scheduled folder for deletion on reboot: {location}")
                        else:
                            self.log(f"Failed to force delete folder and schedule for deletion: {location}: {e}")
                    except Exception as e2:
                        self.log(f"Failed to force delete folder and schedule for deletion: {location}: {e2}")
    
    def remove_leftover_uninstallers(self, software_name, software_info):
        """Remove leftover uninstaller executables (unins*.exe) from software folders"""
        self.log("Removing leftover uninstallers (unins*.exe)...")
        # Locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        if 'InstallLocation' in software_info:
            locations.append(software_info['InstallLocation'])
        for location in locations:
            if os.path.exists(location):
                for file in os.listdir(location):
                    if file.lower().startswith('unins') and file.lower().endswith('.exe'):
                        file_path = os.path.join(location, file)
                        if not self.force_delete_file(file_path):
                            if os.path.exists(file_path):
                                self.log(f"Could not delete {file_path} even after force attempts.")
    
    def clear_log(self):
        """Clear the uninstall log area and log messages list"""
        self.log_text.delete(1.0, tk.END)
        self.log_messages.clear()
    
    def remove_software_registry(self, software_name):
        """Remove registry entries related to the software"""
        self.log(f"Removing registry entries for {software_name}...")
        # Registry roots to check
        registry_roots = [
            (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE"),
            (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
            (winreg.HKEY_USERS, "HKEY_USERS"),
        ]
        removed_count = 0
        for root, root_name in registry_roots:
            try:
                self._remove_registry_recursive(root, "", software_name, root_name, removed_count)
            except Exception as e:
                self.log(f"Error removing from {root_name}: {e}")
        self.log(f"OK: Registry removal complete for {software_name}.")

    def _remove_registry_recursive(self, key, path, software_name, root_name, removed_count, max_depth=3):
        if max_depth <= 0:
            return
        try:
            subkeys = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    subkeys.append(subkey_name)
                except Exception:
                    continue
            for subkey_name in subkeys:
                full_path = f"{root_name}\\{path}\\{subkey_name}" if path else f"{root_name}\\{subkey_name}"
                try:
                    with winreg.OpenKey(key, subkey_name, 0, winreg.KEY_ALL_ACCESS) as subkey:
                        # If software name in subkey, try to delete
                        if software_name.lower() in subkey_name.lower():
                            try:
                                winreg.DeleteKey(key, subkey_name)
                                self.log(f"OK: Deleted registry key: {full_path}")
                                removed_count += 1
                                continue
                            except Exception as e:
                                self.log(f"Error deleting key {full_path}: {e}")
                        # Recurse into subkey
                        self._remove_registry_recursive(subkey, f"{path}\\{subkey_name}" if path else subkey_name, software_name, root_name, removed_count, max_depth-1)
                except Exception:
                    continue
        except Exception:
            pass

    def clean_database_entries(self, software_name):
        """Clean software-related entries in databases"""
        self.log(f"Cleaning database entries for {software_name}...")
        # TODO: Implement actual database cleaning logic
        self.log(f"OK: Database cleaning completed for {software_name}.")

if __name__ == "__main__":
    app = CompleteSoftwareUninstaller()
    app.root.mainloop()