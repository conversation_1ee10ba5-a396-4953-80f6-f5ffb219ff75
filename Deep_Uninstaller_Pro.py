import os
import sys
import shutil
import subprocess
import winreg
import psutil
import sqlite3
import json
import time
import webbrowser
import glob
import re
import ctypes
import tempfile
from pathlib import Path
import tkinter as tk
from tkinter import messagebox, scrolledtext, ttk, filedialog
import threading
from datetime import datetime

# Try to import Windows-specific modules
try:
    import win32gui
    import win32process
    import win32api
    import win32con
    import win32service
    import win32serviceutil
    import win32security
    import win32file
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

# Constants
VERSION = "1.1.0"
BUILD_DATE = "2025-01-06"
DEVELOPER = "AMSSoftX"
WEBSITE = "https://amssoftx.com"

class CompleteSoftwareUninstaller:
    def __init__(self):
        self.log_messages = []  # Initialize log storage first
        self.backup_created = False
        self.scan_results = {}
        self.current_operation = None

        self.root = tk.Tk()
        self.root.title(f"Deep Uninstaller Pro v{VERSION} - {DEVELOPER}")
        # Set a more compact window size instead of full screen
        self.root.geometry("1000x700")
        self.root.minsize(900, 600)

        # Set window icon if available
        try:
            self.root.iconbitmap(default='uninstaller.ico')
        except:
            pass

        # Try to use a modern theme
        style = ttk.Style()
        available_themes = style.theme_names()
        preferred_themes = ["vista", "winnative", "clam", "alt", "default"]
        for theme in preferred_themes:
            if theme in available_themes:
                style.theme_use(theme)
                break

        # Compact modern color scheme
        self.root.configure(bg="#f0f2f5")
        style.configure("TFrame", background="#f0f2f5")
        style.configure("TLabel", background="#f0f2f5", font=("Segoe UI", 9))
        style.configure("TButton", font=("Segoe UI", 9), padding=4)
        style.configure("TCheckbutton", background="#f0f2f5", font=("Segoe UI", 8))
        style.configure("TLabelframe", background="#e8ecf0", font=("Segoe UI", 9, "bold"))
        style.configure("TLabelframe.Label", background="#e8ecf0", font=("Segoe UI", 10, "bold"))
        style.configure("Heading.TLabel", font=("Segoe UI", 12, "bold"), foreground="#1a365d")
        style.configure("Success.TLabel", foreground="#22c55e")
        style.configure("Warning.TLabel", foreground="#f59e0b")
        style.configure("Error.TLabel", foreground="#ef4444")
        
        # Create menu bar
        self.create_menu_bar()

        # Main frame with two columns - compact layout
        main_frame = ttk.Frame(self.root, padding="8 8 8 8")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Left frame for controls - compact
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.N, tk.W, tk.S), padx=(0, 8))

        # Right frame for log - compact
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.N, tk.E, tk.W, tk.S))

        # Header with title and version - compact
        header_frame = ttk.Frame(left_frame)
        header_frame.grid(row=0, column=0, columnspan=2, pady=(0, 8), sticky=(tk.W, tk.E))

        title_label = ttk.Label(header_frame, text="Deep Uninstaller Pro",
                               font=("Segoe UI", 16, "bold"), foreground="#1a365d")
        title_label.grid(row=0, column=0, sticky=tk.W)

        version_label = ttk.Label(header_frame, text=f"v{VERSION}",
                                 font=("Segoe UI", 9), foreground="#64748b")
        version_label.grid(row=0, column=1, sticky=tk.E, padx=(5, 0))

        subtitle_label = ttk.Label(header_frame, text="Complete Software Removal Tool",
                                  font=("Segoe UI", 9), foreground="#64748b")
        subtitle_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(2, 0))
        
        # Software selection section - compact
        selection_frame = ttk.LabelFrame(left_frame, text="Software Selection", padding="6 6 6 6")
        selection_frame.grid(row=1, column=0, columnspan=2, pady=(5, 8), sticky=(tk.W, tk.E))

        ttk.Label(selection_frame, text="Select Software:",
                 font=("Segoe UI", 9, "bold"), foreground="#374151").grid(row=0, column=0, sticky=tk.W, pady=(0, 4))

        # Software selection with search - compact
        software_frame = ttk.Frame(selection_frame)
        software_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 4))

        self.software_var = tk.StringVar()
        self.software_combo = ttk.Combobox(software_frame, textvariable=self.software_var,
                                          width=35, font=("Segoe UI", 9))
        self.software_combo.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 3))
        self.software_combo.bind('<KeyRelease>', self.filter_software_list)

        # Buttons frame - compact
        buttons_frame = ttk.Frame(software_frame)
        buttons_frame.grid(row=0, column=1)

        ttk.Button(buttons_frame, text="🔄", command=self.refresh_software_list, width=4).pack(side=tk.LEFT, padx=1)
        ttk.Button(buttons_frame, text="📁", command=self.browse_for_software, width=4).pack(side=tk.LEFT, padx=1)

        # Software info display - compact
        self.info_var = tk.StringVar()
        self.info_label = ttk.Label(selection_frame, textvariable=self.info_var,
                                   font=("Segoe UI", 8), foreground="#6b7280")
        self.info_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(3, 0))

        # Bind selection change
        self.software_combo.bind('<<ComboboxSelected>>', self.on_software_selected)
        
        # Options frame with tabs - compact
        options_frame = ttk.LabelFrame(left_frame, text="Options", padding="6 6 6 6")
        options_frame.grid(row=2, column=0, columnspan=2, pady=(0, 8), sticky=(tk.W, tk.E))

        # Create notebook for tabbed options - compact
        options_notebook = ttk.Notebook(options_frame)
        options_notebook.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Basic options tab - compact
        basic_frame = ttk.Frame(options_notebook, padding="6 6 6 6")
        options_notebook.add(basic_frame, text="Basic")

        # Advanced options tab - compact
        advanced_frame = ttk.Frame(options_notebook, padding="6 6 6 6")
        options_notebook.add(advanced_frame, text="Advanced")

        # Initialize variables
        self.remove_files = tk.BooleanVar(value=True)
        self.remove_registry = tk.BooleanVar(value=True)
        self.remove_services = tk.BooleanVar(value=True)
        self.remove_tasks = tk.BooleanVar(value=True)
        self.remove_db = tk.BooleanVar(value=True)
        self.remove_uninstallers = tk.BooleanVar(value=True)
        # Advanced options
        self.remove_api_hooks = tk.BooleanVar(value=True)
        self.remove_dlls = tk.BooleanVar(value=True)
        self.remove_com_objects = tk.BooleanVar(value=True)
        self.remove_file_associations = tk.BooleanVar(value=True)
        self.remove_context_menus = tk.BooleanVar(value=True)
        self.remove_firewall_rules = tk.BooleanVar(value=True)
        self.remove_network_connections = tk.BooleanVar(value=True)
        self.remove_startup_entries = tk.BooleanVar(value=True)
        self.remove_system_integration = tk.BooleanVar(value=True)
        self.create_backup = tk.BooleanVar(value=True)
        self.safe_mode = tk.BooleanVar(value=False)

        # Basic options - compact layout
        ttk.Checkbutton(basic_frame, text="Files & Folders", variable=self.remove_files).grid(row=0, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(basic_frame, text="Registry Entries", variable=self.remove_registry).grid(row=1, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(basic_frame, text="Services", variable=self.remove_services).grid(row=2, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(basic_frame, text="Scheduled Tasks", variable=self.remove_tasks).grid(row=0, column=1, sticky=tk.W, pady=1, padx=(10, 0))
        ttk.Checkbutton(basic_frame, text="Database Entries", variable=self.remove_db).grid(row=1, column=1, sticky=tk.W, pady=1, padx=(10, 0))
        ttk.Checkbutton(basic_frame, text="Uninstallers", variable=self.remove_uninstallers).grid(row=2, column=1, sticky=tk.W, pady=1, padx=(10, 0))

        # Advanced options - compact layout
        ttk.Checkbutton(advanced_frame, text="API Hooks", variable=self.remove_api_hooks).grid(row=0, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="DLL Registrations", variable=self.remove_dlls).grid(row=1, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="COM Objects", variable=self.remove_com_objects).grid(row=2, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="File Associations", variable=self.remove_file_associations).grid(row=3, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="Context Menus", variable=self.remove_context_menus).grid(row=4, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="Firewall Rules", variable=self.remove_firewall_rules).grid(row=0, column=1, sticky=tk.W, pady=1, padx=(10, 0))
        ttk.Checkbutton(advanced_frame, text="Network Connections", variable=self.remove_network_connections).grid(row=1, column=1, sticky=tk.W, pady=1, padx=(10, 0))
        ttk.Checkbutton(advanced_frame, text="Startup Entries", variable=self.remove_startup_entries).grid(row=2, column=1, sticky=tk.W, pady=1, padx=(10, 0))
        ttk.Checkbutton(advanced_frame, text="System Integration", variable=self.remove_system_integration).grid(row=3, column=1, sticky=tk.W, pady=1, padx=(10, 0))

        # Safety options - compact
        ttk.Separator(advanced_frame, orient='horizontal').grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        ttk.Checkbutton(advanced_frame, text="Registry Backup", variable=self.create_backup).grid(row=6, column=0, sticky=tk.W, pady=1)
        ttk.Checkbutton(advanced_frame, text="Safe Mode", variable=self.safe_mode).grid(row=6, column=1, sticky=tk.W, pady=1, padx=(10, 0))

        # Quick selection buttons - compact
        quick_frame = ttk.Frame(options_frame)
        quick_frame.grid(row=1, column=0, columnspan=3, pady=(5, 0), sticky=(tk.W, tk.E))

        ttk.Button(quick_frame, text="All", command=self.select_all_options, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(quick_frame, text="None", command=self.select_no_options, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(quick_frame, text="Basic", command=self.select_basic_options, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(quick_frame, text="Advanced", command=self.select_advanced_options, width=8).pack(side=tk.LEFT, padx=1)
        
        # Action buttons - compact layout
        action_frame = ttk.LabelFrame(left_frame, text="Actions", padding="6 6 6 6")
        action_frame.grid(row=3, column=0, columnspan=2, pady=(0, 8), sticky=(tk.W, tk.E))

        # Primary actions - compact
        primary_frame = ttk.Frame(action_frame)
        primary_frame.grid(row=0, column=0, columnspan=3, pady=(0, 5), sticky=(tk.W, tk.E))

        self.scan_button = ttk.Button(primary_frame, text="🔍 Scan", command=self.scan_software, width=12)
        self.scan_button.pack(side=tk.LEFT, padx=2)

        self.uninstall_button = ttk.Button(primary_frame, text="🗑️ Uninstall", command=self.complete_uninstall, width=12)
        self.uninstall_button.pack(side=tk.LEFT, padx=2)
        self.uninstall_button.configure(state='disabled')

        # Secondary actions - compact
        secondary_frame = ttk.Frame(action_frame)
        secondary_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Button(secondary_frame, text="Clear", command=self.clear_log, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(secondary_frame, text="Save", command=self.save_log, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(secondary_frame, text="Report", command=self.view_scan_report, width=8).pack(side=tk.LEFT, padx=1)
        ttk.Button(secondary_frame, text="Settings", command=self.open_settings, width=8).pack(side=tk.LEFT, padx=1)
        
        # Status and progress section - compact
        status_frame = ttk.LabelFrame(left_frame, text="Status", padding="6 6 6 6")
        status_frame.grid(row=4, column=0, columnspan=2, pady=(0, 8), sticky=(tk.W, tk.E))

        # Status label - compact
        self.status_var = tk.StringVar(value="Ready")
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                     font=("Segoe UI", 9, "bold"), foreground="#059669")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 3))

        # Progress bar with percentage - compact
        progress_frame = ttk.Frame(status_frame)
        progress_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 3))

        self.progress = ttk.Progressbar(progress_frame, mode='determinate', length=250)
        self.progress.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.progress_var = tk.StringVar(value="0%")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var, width=5)
        self.progress_label.pack(side=tk.RIGHT, padx=(3, 0))

        # Operation details - compact
        self.operation_var = tk.StringVar(value="")
        self.operation_label = ttk.Label(status_frame, textvariable=self.operation_var,
                                        font=("Segoe UI", 8), foreground="#6b7280")
        self.operation_label.grid(row=2, column=0, sticky=tk.W)
        
        # Enhanced log area in right frame - compact
        log_header_frame = ttk.Frame(right_frame)
        log_header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Label(log_header_frame, text="Operation Log",
                 font=("Segoe UI", 11, "bold"), foreground="#1a365d").pack(side=tk.LEFT)

        # Log controls - compact
        log_controls = ttk.Frame(log_header_frame)
        log_controls.pack(side=tk.RIGHT)

        self.auto_scroll_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(log_controls, text="Auto", variable=self.auto_scroll_var).pack(side=tk.LEFT, padx=3)

        ttk.Button(log_controls, text="📋", command=self.copy_log, width=4).pack(side=tk.LEFT, padx=1)
        ttk.Button(log_controls, text="🔍", command=self.find_in_log, width=4).pack(side=tk.LEFT, padx=1)

        # Log text area - compact
        log_frame = ttk.Frame(right_frame)
        log_frame.grid(row=1, column=0, sticky=(tk.N, tk.S, tk.E, tk.W))

        self.log_text = scrolledtext.ScrolledText(log_frame, width=70, height=35,
                                                 font=("Consolas", 9), bg="#fafbfc",
                                                 wrap=tk.NONE, relief=tk.FLAT, borderwidth=1)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Configure text tags for colored output
        self.log_text.tag_configure("success", foreground="#22c55e")
        self.log_text.tag_configure("warning", foreground="#f59e0b")
        self.log_text.tag_configure("error", foreground="#ef4444")
        self.log_text.tag_configure("info", foreground="#3b82f6")
        self.log_text.tag_configure("timestamp", foreground="#6b7280")

        # Add horizontal scrollbar
        h_scroll = tk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.log_text.xview)
        h_scroll.grid(row=2, column=0, sticky=(tk.W, tk.E))
        self.log_text.config(xscrollcommand=h_scroll.set)
        
        # Developer info
        dev_frame = ttk.Frame(left_frame)
        dev_frame.grid(row=6, column=0, columnspan=2, pady=(10,0))
        ttk.Label(dev_frame, text="Developed By AMSSoftX", font=("Segoe UI", 10)).pack(side=tk.LEFT, padx=5)
        website_label = ttk.Label(dev_frame, text="Website: https://amssoftx.com", font=("Segoe UI", 10), foreground="blue", cursor="hand2")
        website_label.pack(side=tk.LEFT, padx=5)
        website_label.bind("<Button-1>", lambda e: webbrowser.open("https://amssoftx.com"))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # Initialize
        self.software_list = []
        self.filtered_list = []
        self.refresh_software_list()
        self.update_status("Ready - Select software to uninstall")

    def create_menu_bar(self):
        """Create the application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Save Log...", command=self.save_log, accelerator="Ctrl+S")
        file_menu.add_command(label="Load Settings...", command=self.load_settings)
        file_menu.add_command(label="Save Settings...", command=self.save_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit, accelerator="Alt+F4")

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Registry Backup", command=self.create_registry_backup)
        tools_menu.add_command(label="System Restore Point", command=self.create_restore_point)
        tools_menu.add_command(label="Cleanup Temp Files", command=self.cleanup_temp_files)
        tools_menu.add_command(label="Disk Cleanup", command=self.run_disk_cleanup)

        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Scan Report", command=self.view_scan_report)
        view_menu.add_command(label="System Information", command=self.show_system_info)
        view_menu.add_command(label="Installed Programs", command=self.show_installed_programs)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self.show_help)
        help_menu.add_command(label="Keyboard Shortcuts", command=self.show_shortcuts)
        help_menu.add_command(label="Check for Updates", command=self.check_updates)
        help_menu.add_separator()
        help_menu.add_command(label="About", command=self.show_about)

        # Bind keyboard shortcuts
        self.root.bind('<Control-s>', lambda e: self.save_log())
        self.root.bind('<F1>', lambda e: self.show_help())
        self.root.bind('<F5>', lambda e: self.refresh_software_list())
    
    def log(self, message, level="info"):
        """Add message to log with enhanced formatting"""
        timestamp = time.strftime("%H:%M:%S")
        full_message = f"[{timestamp}] {message}"
        self.log_messages.append(full_message)

        # Determine message type and color
        tag = "info"
        if "✅" in message or "SUCCESS" in message.upper() or "completed successfully" in message.lower():
            tag = "success"
        elif "⚠️" in message or "WARNING" in message.upper() or "warning" in message.lower():
            tag = "warning"
        elif "❌" in message or "ERROR" in message.upper() or "error" in message.lower() or "failed" in message.lower():
            tag = "error"

        # Update UI with colored text
        self.log_text.insert(tk.END, f"[{timestamp}] ", "timestamp")
        self.log_text.insert(tk.END, f"{message}\n", tag)

        if self.auto_scroll_var.get():
            self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, status, operation=""):
        """Update status display"""
        self.status_var.set(status)
        self.operation_var.set(operation)
        self.current_operation = operation
        self.root.update_idletasks()

    def update_progress(self, value, maximum=100):
        """Update progress bar"""
        if maximum > 0:
            percentage = int((value / maximum) * 100)
            self.progress['value'] = percentage
            self.progress_var.set(f"{percentage}%")
        else:
            self.progress['value'] = 0
            self.progress_var.set("0%")
        self.root.update_idletasks()

    def filter_software_list(self, event=None):
        """Filter software list based on search text"""
        search_text = self.software_var.get().lower()
        if not search_text:
            self.software_combo['values'] = self.software_list
        else:
            filtered = [item for item in self.software_list if search_text in item.lower()]
            self.software_combo['values'] = filtered

    def on_software_selected(self, event=None):
        """Handle software selection"""
        software_name = self.software_var.get()
        if software_name:
            self.uninstall_button.configure(state='normal')
            # Get basic info
            software_info = self.find_software_info(software_name)
            if software_info:
                install_location = software_info.get('InstallLocation', 'Unknown')
                version = software_info.get('DisplayVersion', 'Unknown')
                self.info_var.set(f"Version: {version} | Location: {install_location}")
            else:
                self.info_var.set("Software information not available")
        else:
            self.uninstall_button.configure(state='disabled')
            self.info_var.set("")
    
    def select_all_options(self):
        """Select all uninstall options"""
        for var in [self.remove_files, self.remove_registry, self.remove_services,
                   self.remove_tasks, self.remove_db, self.remove_uninstallers,
                   self.remove_api_hooks, self.remove_dlls, self.remove_com_objects,
                   self.remove_file_associations, self.remove_context_menus,
                   self.remove_firewall_rules, self.remove_network_connections,
                   self.remove_startup_entries, self.remove_system_integration]:
            var.set(True)

    def select_no_options(self):
        """Deselect all uninstall options"""
        for var in [self.remove_files, self.remove_registry, self.remove_services,
                   self.remove_tasks, self.remove_db, self.remove_uninstallers,
                   self.remove_api_hooks, self.remove_dlls, self.remove_com_objects,
                   self.remove_file_associations, self.remove_context_menus,
                   self.remove_firewall_rules, self.remove_network_connections,
                   self.remove_startup_entries, self.remove_system_integration]:
            var.set(False)

    def select_basic_options(self):
        """Select only basic uninstall options"""
        # Basic options
        for var in [self.remove_files, self.remove_registry, self.remove_services,
                   self.remove_tasks, self.remove_db, self.remove_uninstallers]:
            var.set(True)
        # Advanced options
        for var in [self.remove_api_hooks, self.remove_dlls, self.remove_com_objects,
                   self.remove_file_associations, self.remove_context_menus,
                   self.remove_firewall_rules, self.remove_network_connections,
                   self.remove_startup_entries, self.remove_system_integration]:
            var.set(False)

    def select_advanced_options(self):
        """Select only advanced uninstall options"""
        # Basic options
        for var in [self.remove_files, self.remove_registry, self.remove_services,
                   self.remove_tasks, self.remove_db, self.remove_uninstallers]:
            var.set(False)
        # Advanced options
        for var in [self.remove_api_hooks, self.remove_dlls, self.remove_com_objects,
                   self.remove_file_associations, self.remove_context_menus,
                   self.remove_firewall_rules, self.remove_network_connections,
                   self.remove_startup_entries, self.remove_system_integration]:
            var.set(True)

    def browse_for_software(self):
        """Browse for software executable to uninstall"""
        file_path = filedialog.askopenfilename(
            title="Select Software Executable",
            filetypes=[("Executable files", "*.exe"), ("All files", "*.*")]
        )
        if file_path:
            software_name = os.path.basename(file_path).replace('.exe', '')
            self.software_var.set(software_name)
            self.log(f"Selected software executable: {file_path}")

    def refresh_software_list(self):
        """Refresh the list of installed software"""
        self.update_status("Refreshing software list...", "Scanning registry...")
        self.log("🔄 Refreshing software list...")

        try:
            self.software_list = self.get_installed_software()
            self.software_combo['values'] = self.software_list
            self.filtered_list = self.software_list.copy()

            self.log(f"✅ Found {len(self.software_list)} installed programs")
            self.update_status("Ready - Select software to uninstall")
        except Exception as e:
            self.log(f"❌ Error refreshing software list: {e}", "error")
            self.update_status("Error refreshing list")
    
    def get_installed_software(self):
        """Get list of installed software from registry"""
        software_list = []
        
        # Registry paths to check
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if display_name and display_name not in software_list:
                                        software_list.append(display_name)
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue
            except Exception as e:
                self.log(f"Error reading registry: {e}")
        
        return sorted(software_list)
    
    def scan_software(self):
        """Scan selected software for all components"""
        software_name = self.software_var.get()
        if not software_name:
            messagebox.showwarning("Warning", "Please select a software to scan")
            return
        
        self.log(f"Scanning {software_name}...")
        
        # Start scanning in separate thread
        thread = threading.Thread(target=self._scan_software_thread, args=(software_name,))
        thread.daemon = True
        thread.start()
    
    def _scan_software_thread(self, software_name):
        """Scan software in separate thread with enhanced progress tracking"""
        try:
            self.update_status("Scanning software...", f"Analyzing {software_name}")
            self.scan_results = {'software_name': software_name}

            total_steps = 6
            current_step = 0

            # Step 1: Find software info
            current_step += 1
            self.update_progress(current_step, total_steps)
            self.update_status("Scanning software...", "Finding software information...")

            software_info = self.find_software_info(software_name)

            if software_info:
                self.log(f"✅ Found software: {software_name}")
                self.log(f"📍 Install Location: {software_info.get('InstallLocation', 'Not found')}")
                self.log(f"🔧 Uninstall String: {software_info.get('UninstallString', 'Not found')}")

                # Step 2: Scan for files
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Scanning software...", "Scanning files and folders...")
                found_files = self.scan_files(software_name, software_info)
                self.scan_results['files'] = found_files

                # Step 3: Scan for registry entries
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Scanning software...", "Scanning registry entries...")
                found_registry = self.scan_registry_entries(software_name)
                self.scan_results['registry'] = found_registry

                # Step 4: Scan for services
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Scanning software...", "Scanning services...")
                found_services = self.scan_services(software_name)
                self.scan_results['services'] = found_services

                # Step 5: Scan for scheduled tasks
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Scanning software...", "Scanning scheduled tasks...")
                found_tasks = self.scan_scheduled_tasks(software_name)
                self.scan_results['tasks'] = found_tasks

                # Step 6: Complete scan
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Scan completed", f"Found components for {software_name}")

                self.log(f"✅ Scan completed for {software_name}")

                # Enable uninstall button
                self.uninstall_button.configure(state='normal')

            else:
                self.log(f"❌ Software {software_name} not found in registry")
                self.update_status("Scan failed", "Software not found in registry")

        except Exception as e:
            self.log(f"❌ Error during scan: {e}", "error")
            self.update_status("Scan failed", f"Error: {str(e)}")
        finally:
            self.update_progress(0, 100)  # Reset progress bar
    
    def find_software_info(self, software_name):
        """Find software information in registry"""
        registry_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]
        
        for path in registry_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if display_name == software_name:
                                        # Get all values
                                        software_info = {}
                                        for j in range(winreg.QueryInfoKey(subkey)[1]):
                                            try:
                                                value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                                software_info[value_name] = value_data
                                            except Exception:
                                                continue
                                        return software_info
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue
            except Exception:
                continue
        return None
    
    def scan_files(self, software_name, software_info):
        """Scan for software files and folders"""
        self.log("Scanning for files and folders...")
        
        # Common locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        
        # Add install location if available
        if 'InstallLocation' in software_info:
            locations.append(software_info['InstallLocation'])
        
        found_paths = []
        for location in locations:
            if os.path.exists(location):
                found_paths.append(location)
                self.log(f"Found folder: {location}")
        
        # Scan for files in common locations
        common_files = [
            os.path.join(os.environ.get('TEMP', ''), f"{software_name}*"),
            os.path.join(os.environ.get('WINDIR', ''), 'Temp', f"{software_name}*"),
        ]
        
        return found_paths
    
    def scan_registry_entries(self, software_name):
        """Scan for registry entries"""
        self.log("Scanning for registry entries...")
        
        # Registry roots to check
        registry_roots = [
            (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE"),
            (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
            (winreg.HKEY_USERS, "HKEY_USERS"),
        ]
        
        found_entries = []
        for root, root_name in registry_roots:
            try:
                self._scan_registry_recursive(root, "", software_name, found_entries, root_name)
            except Exception as e:
                self.log(f"Error scanning {root_name}: {e}")
        
        self.log(f"Found {len(found_entries)} registry entries")
        return found_entries
    
    def _scan_registry_recursive(self, key, path, software_name, found_entries, root_name, max_depth=3):
        """Recursively scan registry for software entries"""
        if max_depth <= 0:
            return
        
        try:
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    if software_name.lower() in subkey_name.lower():
                        full_path = f"{root_name}\\{path}\\{subkey_name}" if path else f"{root_name}\\{subkey_name}"
                        found_entries.append(full_path)
                        self.log(f"Found registry key: {full_path}")
                    
                    # Recurse into subkey
                    try:
                        with winreg.OpenKey(key, subkey_name) as subkey:
                            new_path = f"{path}\\{subkey_name}" if path else subkey_name
                            self._scan_registry_recursive(subkey, new_path, software_name, found_entries, root_name, max_depth-1)
                    except Exception:
                        pass
                except Exception:
                    continue
        except Exception:
            pass
    
    def scan_services(self, software_name):
        """Scan for related services"""
        self.log("Scanning for services...")
        
        found_services = []
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    found_services.append(service_info)
                    self.log(f"Found service: {service_info['name']} ({service_info['display_name']})")
            except Exception:
                continue
        
        return found_services
    
    def scan_scheduled_tasks(self, software_name):
        """Scan for scheduled tasks"""
        self.log("Scanning for scheduled tasks...")
        
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                found_tasks = []
                for line in lines[1:]:  # Skip header
                    if software_name.lower() in line.lower():
                        found_tasks.append(line)
                        self.log(f"Found scheduled task: {line}")
                return found_tasks
        except Exception as e:
            self.log(f"Error scanning scheduled tasks: {e}")
        
        return []
    
    def complete_uninstall(self):
        """Perform complete uninstall"""
        software_name = self.software_var.get()
        if not software_name:
            messagebox.showwarning("Warning", "Please select a software to uninstall")
            return
        
        # Confirmation dialog
        if not messagebox.askyesno("Confirm Uninstall", 
                                  f"Are you sure you want to completely uninstall {software_name}?\n\n"
                                  "This will remove ALL traces of the software including:\n"
                                  "- Files and folders\n"
                                  "- Registry entries\n"
                                  "- Services\n"
                                  "- Scheduled tasks\n"
                                  "- Database entries\n\n"
                                  "This action cannot be undone!"):
            return
        
        # Start uninstall in separate thread
        thread = threading.Thread(target=self._complete_uninstall_thread, args=(software_name,))
        thread.daemon = True
        thread.start()
    
    def _complete_uninstall_thread(self, software_name):
        """Complete uninstall in separate thread with enhanced progress tracking"""
        try:
            self.update_status("Uninstalling...", f"Starting removal of {software_name}")
            self.log(f"🚀 Starting complete uninstall of {software_name}...")

            # Calculate total steps based on selected options
            total_steps = 1  # Basic setup
            if self.remove_services.get(): total_steps += 1
            if self.remove_tasks.get(): total_steps += 1
            total_steps += 1  # Official uninstaller
            if self.remove_files.get(): total_steps += 1
            if self.remove_registry.get(): total_steps += 1
            if self.remove_db.get(): total_steps += 1
            if self.remove_api_hooks.get(): total_steps += 1
            if self.remove_dlls.get(): total_steps += 1
            if self.remove_com_objects.get(): total_steps += 1
            if self.remove_file_associations.get(): total_steps += 1
            if self.remove_context_menus.get(): total_steps += 1
            if self.remove_firewall_rules.get(): total_steps += 1
            if self.remove_network_connections.get(): total_steps += 1
            if self.remove_startup_entries.get(): total_steps += 1
            if self.remove_system_integration.get(): total_steps += 1
            total_steps += 2  # Verification and cleanup

            current_step = 0

            # Step 1: Find software info and stop processes
            current_step += 1
            self.update_progress(current_step, total_steps)
            self.update_status("Uninstalling...", "Finding software information...")

            software_info = self.find_software_info(software_name)
            if not software_info:
                self.log(f"❌ Software {software_name} not found in registry")
                self.update_status("Uninstall failed", "Software not found")
                return

            self.update_status("Uninstalling...", "Stopping related processes...")
            self.stop_related_processes(software_name)

            # Services
            if self.remove_services.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Removing services...")
                self.remove_software_services(software_name)

            # Tasks
            if self.remove_tasks.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Removing scheduled tasks...")
                self.remove_software_tasks(software_name)

            # Official uninstaller
            current_step += 1
            self.update_progress(current_step, total_steps)
            self.update_status("Uninstalling...", "Running official uninstaller...")
            if 'UninstallString' in software_info:
                self.run_official_uninstaller(software_info['UninstallString'])

            # Files
            if self.remove_files.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Removing files and folders...")
                self.remove_software_files(software_name, software_info)

            # Registry
            if self.remove_registry.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning registry...")
                self.remove_software_registry(software_name)

            # Database
            if self.remove_db.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning database entries...")
                self.clean_database_entries(software_name)

            # Advanced cleanup options
            if self.remove_api_hooks.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning API hooks...")
                self.clean_windows_api_hooks(software_name)

            if self.remove_dlls.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning DLL registrations...")
                self.clean_dll_registrations(software_name)

            if self.remove_com_objects.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning COM objects...")
                self.clean_com_objects(software_name)

            if self.remove_file_associations.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning file associations...")
                self.clean_file_associations(software_name)

            if self.remove_context_menus.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning context menus...")
                self.clean_context_menu_entries(software_name)

            if self.remove_firewall_rules.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning firewall rules...")
                self.clean_firewall_rules(software_name)

            if self.remove_network_connections.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning network connections...")
                self.clean_network_connections(software_name)

            if self.remove_startup_entries.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning startup entries...")
                self.clean_startup_entries(software_name)

            if self.remove_system_integration.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Cleaning system integration...")
                self.clean_system_integration(software_name)

            # Verification
            current_step += 1
            self.update_progress(current_step, total_steps)
            self.update_status("Uninstalling...", "Verifying removal...")
            self.verify_removal(software_name)

            # Final cleanup
            if self.remove_uninstallers.get():
                current_step += 1
                self.update_progress(current_step, total_steps)
                self.update_status("Uninstalling...", "Removing leftover uninstallers...")
                self.remove_leftover_uninstallers(software_name, software_info)

            # Complete
            self.update_progress(total_steps, total_steps)
            self.update_status("Uninstall completed", f"Successfully removed {software_name}")
            self.log(f"✅ Complete uninstall of {software_name} finished!")

            # Refresh software list
            self.refresh_software_list()

            messagebox.showinfo("Success", f"✅ {software_name} has been completely uninstalled!\n\nAll selected components have been removed from your system.")

        except Exception as e:
            self.log(f"❌ Error during uninstall: {e}", "error")
            self.update_status("Uninstall failed", f"Error: {str(e)}")
            messagebox.showerror("Error", f"❌ Error during uninstall:\n\n{e}\n\nCheck the log for more details.")
        finally:
            self.update_progress(0, 100)  # Reset progress bar
    
    def verify_removal(self, software_name):
        """Verify that all traces of the software are removed"""
        self.log(f"Verifying removal of {software_name}...")
        # Check for remaining files/folders
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        for location in locations:
            if os.path.exists(location):
                self.log(f"Warning: Folder still exists: {location}")
        # Check for running processes
        for proc in psutil.process_iter(['name', 'exe', 'cmdline']):
            try:
                if software_name.lower() in proc.info['name'].lower():
                    self.log(f"Warning: Process still running: {proc.info['name']}")
            except Exception:
                continue
        # Check for registry entries
        found_entries = self.scan_registry_entries(software_name)
        if found_entries:
            self.log(f"Warning: Registry entries still found: {len(found_entries)}")
        # Check for services
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    self.log(f"Warning: Service still exists: {service_info['name']}")
            except Exception:
                continue
        # Check for scheduled tasks
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:
                    if software_name.lower() in line.lower():
                        self.log(f"Warning: Scheduled task still exists: {line}")
        except Exception:
            pass
        self.log(f"Verification complete for {software_name}.")

    def stop_related_processes(self, software_name):
        """Stop all processes related to the software - Advanced Task Manager Kill"""
        self.log("Stopping related processes from Task Manager...")
        
        stopped_processes = []
        force_killed = []
        
        # Phase 1: Find ALL related processes (including child processes)
        target_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'cwd']):
            try:
                # Check process name
                if software_name.lower() in proc.info['name'].lower():
                    target_processes.append(proc)
                    continue
                
                # Check executable path
                if proc.info['exe'] and software_name.lower() in proc.info['exe'].lower():
                    target_processes.append(proc)
                    continue
                
                # Check command line arguments
                if proc.info['cmdline']:
                    cmdline_str = ' '.join(proc.info['cmdline']).lower()
                    if software_name.lower() in cmdline_str:
                        target_processes.append(proc)
                        continue
                
                # Check working directory
                if proc.info['cwd'] and software_name.lower() in proc.info['cwd'].lower():
                    target_processes.append(proc)
                    continue
                    
            except (psutil.NoSuchProcess, psutil.AccessDenied, TypeError):
                continue
        
        self.log(f"Found {len(target_processes)} related processes")
        
        # Phase 2: Stop child processes first
        for proc in target_processes:
            try:
                # Get all children
                children = proc.children(recursive=True)
                for child in children:
                    try:
                        child.terminate()
                        self.log(f"Terminated child process: {child.name()} (PID: {child.pid})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
                # Terminate main process
                proc.terminate()
                stopped_processes.append(proc.info['name'])
                self.log(f"Terminated process: {proc.info['name']} (PID: {proc.info['pid']})")
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Wait for graceful termination
        time.sleep(3)
        
        # Phase 3: Force kill remaining processes
        for proc in target_processes:
            try:
                if proc.is_running():
                    # Try using Windows taskkill command for stubborn processes
                    try:
                        subprocess.run(['taskkill', '/F', '/PID', str(proc.pid)], 
                                     capture_output=True, check=False)
                        self.log(f"Force killed with taskkill: {proc.name()} (PID: {proc.pid})")
                    except:
                        pass
                    
                    # Final attempt with psutil
                    try:
                        proc.kill()
                        force_killed.append(proc.name())
                        self.log(f"Force killed process: {proc.name()} (PID: {proc.pid})")
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # Phase 4: Kill by process name using taskkill (catch any remaining)
        try:
            subprocess.run(['taskkill', '/F', '/IM', f"{software_name}*"], 
                         capture_output=True, check=False)
            self.log(f"Executed taskkill for all {software_name} processes")
        except Exception:
            pass
        
        # Phase 5: Advanced process cleanup
        self.advanced_process_cleanup(software_name)
        
        self.log(f"Process cleanup completed: {len(stopped_processes)} terminated, {len(force_killed)} force killed")
    
    def advanced_process_cleanup(self, software_name):
        """Advanced process cleanup including handles and memory"""
        self.log("Performing advanced process cleanup...")

        try:
            if WIN32_AVAILABLE:
                # Kill processes by window title
                def enum_windows_callback(hwnd, windows):
                    if win32gui.IsWindowVisible(hwnd):
                        window_title = win32gui.GetWindowText(hwnd)
                        if software_name.lower() in window_title.lower():
                            try:
                                pid = win32process.GetWindowThreadProcessId(hwnd)[1]
                                process = psutil.Process(pid)
                                if process.is_running():
                                    process.terminate()
                                    windows.append(f"Terminated process by window title: {window_title} (PID: {pid})")
                            except Exception:
                                pass
                # Enum windows and terminate processes
                windows = []
                win32gui.EnumWindows(enum_windows_callback, windows)
                for message in windows:
                    self.log(message)
            else:
                self.log("Win32 modules not available, skipping window-based process cleanup")

            # Alternative cleanup using tasklist and taskkill
            try:
                result = subprocess.run(['tasklist', '/fo', 'csv'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if software_name.lower() in line.lower():
                            # Extract process name and PID
                            parts = line.split(',')
                            if len(parts) >= 2:
                                process_name = parts[0].strip('"')
                                pid = parts[1].strip('"')
                                try:
                                    subprocess.run(['taskkill', '/F', '/PID', pid],
                                                 capture_output=True, check=False)
                                    self.log(f"Force killed process: {process_name} (PID: {pid})")
                                except Exception:
                                    pass
            except Exception:
                pass

        except Exception as e:
            self.log(f"Error in advanced process cleanup: {e}")
    
    def clean_windows_api_hooks(self, software_name):
        """Clean Windows API hooks and injected DLLs"""
        self.log(f"[Advanced] Cleaning Windows API hooks for {software_name}...")

        try:
            # Check for DLL injection in running processes
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    process = psutil.Process(proc.info['pid'])
                    # Get loaded modules/DLLs
                    try:
                        for dll in process.memory_maps():
                            if software_name.lower() in dll.path.lower():
                                self.log(f"Found injected DLL: {dll.path} in process {proc.info['name']}")
                                # Try to terminate the process to remove the hook
                                try:
                                    process.terminate()
                                    self.log(f"Terminated process {proc.info['name']} to remove API hook")
                                except:
                                    pass
                    except (psutil.AccessDenied, psutil.NoSuchProcess):
                        continue
                except Exception:
                    continue

            # Check for system-wide hooks in registry
            hook_keys = [
                r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Windows\AppInit_DLLs",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows NT\CurrentVersion\Windows\AppInit_DLLs",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ShellExecuteHooks",
                r"SOFTWARE\Classes\*\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\Directory\shellex\ContextMenuHandlers"
            ]

            for key_path in hook_keys:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path, 0, winreg.KEY_READ | winreg.KEY_WRITE) as key:
                        # Check values for software references
                        for i in range(winreg.QueryInfoKey(key)[1]):
                            try:
                                value_name, value_data, _ = winreg.EnumValue(key, i)
                                if isinstance(value_data, str) and software_name.lower() in value_data.lower():
                                    try:
                                        winreg.DeleteValue(key, value_name)
                                        self.log(f"Removed API hook registry value: {key_path}\\{value_name}")
                                    except Exception as e:
                                        self.log(f"Error removing hook value {value_name}: {e}")
                            except Exception:
                                continue
                except Exception:
                    continue

        except Exception as e:
            self.log(f"Error cleaning API hooks: {e}")

        self.log(f"[Advanced] API hooks cleanup completed for {software_name}")
    
    def clean_dll_registrations(self, software_name):
        """Clean DLL registrations and COM components"""
        self.log(f"[Advanced] Cleaning DLL registrations for {software_name}...")

        try:
            # Find DLL files related to the software
            dll_locations = [
                os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
                os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
                os.path.join(os.environ.get('SYSTEM32', os.path.join(os.environ.get('WINDIR', ''), 'System32'))),
                os.path.join(os.environ.get('WINDIR', ''), 'SysWOW64'),
            ]

            dll_files = []
            for location in dll_locations:
                if os.path.exists(location):
                    for root, dirs, files in os.walk(location):
                        for file in files:
                            if (file.lower().endswith('.dll') and
                                software_name.lower() in file.lower()):
                                dll_files.append(os.path.join(root, file))

            # Unregister DLLs
            for dll_file in dll_files:
                try:
                    # Try to unregister the DLL
                    result = subprocess.run(['regsvr32', '/u', '/s', dll_file],
                                          capture_output=True, check=False)
                    if result.returncode == 0:
                        self.log(f"Unregistered DLL: {dll_file}")
                    else:
                        self.log(f"Could not unregister DLL: {dll_file}")
                except Exception as e:
                    self.log(f"Error unregistering DLL {dll_file}: {e}")

            # Clean DLL cache
            try:
                subprocess.run(['sfc', '/scannow'], capture_output=True, check=False)
                self.log("System file checker scan initiated")
            except Exception:
                pass

            # Clean registry entries for DLLs
            dll_registry_paths = [
                r"SOFTWARE\Classes\CLSID",
                r"SOFTWARE\Classes\Interface",
                r"SOFTWARE\Classes\TypeLib",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\SharedDLLs"
            ]

            for reg_path in dll_registry_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                        self._clean_dll_registry_entries(key, software_name, reg_path)
                except Exception:
                    continue

        except Exception as e:
            self.log(f"Error cleaning DLL registrations: {e}")

        self.log(f"[Advanced] DLL registrations cleanup completed for {software_name}")

    def _clean_dll_registry_entries(self, key, software_name, base_path):
        """Helper method to clean DLL-related registry entries"""
        try:
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        # Check if this entry references our software
                        for j in range(winreg.QueryInfoKey(subkey)[1]):
                            try:
                                value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                if (isinstance(value_data, str) and
                                    software_name.lower() in value_data.lower()):
                                    # Try to delete the entire subkey
                                    try:
                                        winreg.DeleteKey(key, subkey_name)
                                        self.log(f"Removed DLL registry key: {base_path}\\{subkey_name}")
                                        break
                                    except Exception:
                                        pass
                            except Exception:
                                continue
                except Exception:
                    continue
        except Exception:
            pass
    
    def clean_com_objects(self, software_name):
        """Clean COM objects and interfaces"""
        self.log(f"[Advanced] Cleaning COM objects for {software_name}...")

        try:
            # Registry paths for COM objects
            com_paths = [
                r"SOFTWARE\Classes\CLSID",
                r"SOFTWARE\Classes\Interface",
                r"SOFTWARE\Classes\TypeLib",
                r"SOFTWARE\Classes\AppID",
                r"SOFTWARE\WOW6432Node\Classes\CLSID",
                r"SOFTWARE\WOW6432Node\Classes\Interface",
                r"SOFTWARE\WOW6432Node\Classes\TypeLib",
                r"SOFTWARE\WOW6432Node\Classes\AppID"
            ]

            removed_count = 0
            for com_path in com_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, com_path) as key:
                        removed_count += self._clean_com_registry_entries(key, software_name, com_path)
                except Exception:
                    continue

            # Clean HKEY_CURRENT_USER COM entries
            for com_path in com_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, com_path) as key:
                        removed_count += self._clean_com_registry_entries(key, software_name, com_path)
                except Exception:
                    continue

            self.log(f"Removed {removed_count} COM object entries")

        except Exception as e:
            self.log(f"Error cleaning COM objects: {e}")

        self.log(f"[Advanced] COM objects cleanup completed for {software_name}")

    def _clean_com_registry_entries(self, key, software_name, base_path):
        """Helper method to clean COM-related registry entries"""
        removed_count = 0
        try:
            subkeys_to_delete = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        # Check all values in this subkey
                        should_delete = False
                        for j in range(winreg.QueryInfoKey(subkey)[1]):
                            try:
                                value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                if isinstance(value_data, str):
                                    if (software_name.lower() in value_data.lower() or
                                        software_name.lower() in value_name.lower()):
                                        should_delete = True
                                        break
                            except Exception:
                                continue

                        # Also check subkeys for references
                        if not should_delete:
                            try:
                                for k in range(winreg.QueryInfoKey(subkey)[0]):
                                    try:
                                        sub_subkey_name = winreg.EnumKey(subkey, k)
                                        with winreg.OpenKey(subkey, sub_subkey_name) as sub_subkey:
                                            for l in range(winreg.QueryInfoKey(sub_subkey)[1]):
                                                try:
                                                    val_name, val_data, _ = winreg.EnumValue(sub_subkey, l)
                                                    if (isinstance(val_data, str) and
                                                        software_name.lower() in val_data.lower()):
                                                        should_delete = True
                                                        break
                                                except Exception:
                                                    continue
                                            if should_delete:
                                                break
                                    except Exception:
                                        continue
                            except Exception:
                                pass

                        if should_delete:
                            subkeys_to_delete.append(subkey_name)

                except Exception:
                    continue

            # Delete the identified subkeys
            for subkey_name in subkeys_to_delete:
                try:
                    self._delete_registry_key_recursive(key, subkey_name)
                    self.log(f"Removed COM registry key: {base_path}\\{subkey_name}")
                    removed_count += 1
                except Exception as e:
                    self.log(f"Error removing COM key {subkey_name}: {e}")

        except Exception:
            pass

        return removed_count

    def _delete_registry_key_recursive(self, parent_key, key_name):
        """Recursively delete a registry key and all its subkeys"""
        try:
            with winreg.OpenKey(parent_key, key_name, 0, winreg.KEY_ALL_ACCESS) as key:
                # Delete all subkeys first
                subkeys = []
                for i in range(winreg.QueryInfoKey(key)[0]):
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        subkeys.append(subkey_name)
                    except Exception:
                        continue

                for subkey_name in subkeys:
                    self._delete_registry_key_recursive(key, subkey_name)

            # Now delete the key itself
            winreg.DeleteKey(parent_key, key_name)
        except Exception:
            pass
    
    def clean_file_associations(self, software_name):
        """Clean file associations and default programs"""
        self.log(f"[Advanced] Cleaning file associations for {software_name}...")

        try:
            # Registry paths for file associations
            association_paths = [
                r"SOFTWARE\Classes",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\FileExts",
                r"SOFTWARE\RegisteredApplications",
                r"SOFTWARE\Clients"
            ]

            removed_count = 0
            for assoc_path in association_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, assoc_path) as key:
                        removed_count += self._clean_file_association_entries(key, software_name, assoc_path, winreg.HKEY_LOCAL_MACHINE)
                except Exception:
                    continue

            # Also check HKEY_CURRENT_USER
            for assoc_path in association_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, assoc_path) as key:
                        removed_count += self._clean_file_association_entries(key, software_name, assoc_path, winreg.HKEY_CURRENT_USER)
                except Exception:
                    continue

            # Clean default programs registry
            try:
                default_programs_path = r"SOFTWARE\Microsoft\Windows\Shell\Associations\ApplicationAssociationToasts"
                with winreg.OpenKey(winreg.HKEY_CURRENT_USER, default_programs_path) as key:
                    values_to_delete = []
                    for i in range(winreg.QueryInfoKey(key)[1]):
                        try:
                            value_name, value_data, _ = winreg.EnumValue(key, i)
                            if software_name.lower() in value_name.lower():
                                values_to_delete.append(value_name)
                        except Exception:
                            continue

                    for value_name in values_to_delete:
                        try:
                            winreg.DeleteValue(key, value_name)
                            self.log(f"Removed file association toast: {value_name}")
                            removed_count += 1
                        except Exception:
                            pass
            except Exception:
                pass

            self.log(f"Removed {removed_count} file association entries")

        except Exception as e:
            self.log(f"Error cleaning file associations: {e}")

        self.log(f"[Advanced] File associations cleanup completed for {software_name}")

    def _clean_file_association_entries(self, key, software_name, base_path, root_key):
        """Helper method to clean file association registry entries"""
        removed_count = 0
        try:
            subkeys_to_delete = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        should_delete = False

                        # Check if subkey name contains software name
                        if software_name.lower() in subkey_name.lower():
                            should_delete = True
                        else:
                            # Check values in subkey
                            for j in range(winreg.QueryInfoKey(subkey)[1]):
                                try:
                                    value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                    if isinstance(value_data, str) and software_name.lower() in value_data.lower():
                                        should_delete = True
                                        break
                                except Exception:
                                    continue

                            # Check nested subkeys
                            if not should_delete:
                                try:
                                    for k in range(winreg.QueryInfoKey(subkey)[0]):
                                        try:
                                            nested_key_name = winreg.EnumKey(subkey, k)
                                            with winreg.OpenKey(subkey, nested_key_name) as nested_key:
                                                for l in range(winreg.QueryInfoKey(nested_key)[1]):
                                                    try:
                                                        val_name, val_data, _ = winreg.EnumValue(nested_key, l)
                                                        if (isinstance(val_data, str) and
                                                            software_name.lower() in val_data.lower()):
                                                            should_delete = True
                                                            break
                                                    except Exception:
                                                        continue
                                                if should_delete:
                                                    break
                                        except Exception:
                                            continue
                                except Exception:
                                    pass

                        if should_delete:
                            subkeys_to_delete.append(subkey_name)

                except Exception:
                    continue

            # Delete identified subkeys
            for subkey_name in subkeys_to_delete:
                try:
                    self._delete_registry_key_recursive(key, subkey_name)
                    root_name = "HKEY_LOCAL_MACHINE" if root_key == winreg.HKEY_LOCAL_MACHINE else "HKEY_CURRENT_USER"
                    self.log(f"Removed file association: {root_name}\\{base_path}\\{subkey_name}")
                    removed_count += 1
                except Exception as e:
                    self.log(f"Error removing association key {subkey_name}: {e}")

        except Exception:
            pass

        return removed_count
    
    def clean_context_menu_entries(self, software_name):
        """Clean context menu entries and shell extensions"""
        self.log(f"[Advanced] Cleaning context menu entries for {software_name}...")

        try:
            # Registry paths for context menu entries
            context_menu_paths = [
                r"SOFTWARE\Classes\*\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\AllFileSystemObjects\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\Directory\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\Directory\Background\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\Drive\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\Folder\shellex\ContextMenuHandlers",
                r"SOFTWARE\Classes\*\shell",
                r"SOFTWARE\Classes\Directory\shell",
                r"SOFTWARE\Classes\Directory\Background\shell",
                r"SOFTWARE\Classes\Drive\shell",
                r"SOFTWARE\Classes\Folder\shell"
            ]

            removed_count = 0
            for menu_path in context_menu_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, menu_path) as key:
                        removed_count += self._clean_context_menu_registry(key, software_name, menu_path, "HKEY_LOCAL_MACHINE")
                except Exception:
                    continue

            # Also check HKEY_CURRENT_USER
            for menu_path in context_menu_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, menu_path) as key:
                        removed_count += self._clean_context_menu_registry(key, software_name, menu_path, "HKEY_CURRENT_USER")
                except Exception:
                    continue

            # Clean shell extension approvals
            try:
                approval_path = r"SOFTWARE\Microsoft\Windows\CurrentVersion\Shell Extensions\Approved"
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, approval_path, 0, winreg.KEY_ALL_ACCESS) as key:
                    values_to_delete = []
                    for i in range(winreg.QueryInfoKey(key)[1]):
                        try:
                            value_name, value_data, _ = winreg.EnumValue(key, i)
                            if (isinstance(value_data, str) and
                                software_name.lower() in value_data.lower()):
                                values_to_delete.append(value_name)
                        except Exception:
                            continue

                    for value_name in values_to_delete:
                        try:
                            winreg.DeleteValue(key, value_name)
                            self.log(f"Removed shell extension approval: {value_name}")
                            removed_count += 1
                        except Exception:
                            pass
            except Exception:
                pass

            self.log(f"Removed {removed_count} context menu entries")

        except Exception as e:
            self.log(f"Error cleaning context menu entries: {e}")

        self.log(f"[Advanced] Context menu cleanup completed for {software_name}")

    def _clean_context_menu_registry(self, key, software_name, base_path, root_name):
        """Helper method to clean context menu registry entries"""
        removed_count = 0
        try:
            subkeys_to_delete = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    should_delete = False

                    # Check if subkey name contains software name
                    if software_name.lower() in subkey_name.lower():
                        should_delete = True
                    else:
                        # Check subkey values and nested keys
                        try:
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                # Check values
                                for j in range(winreg.QueryInfoKey(subkey)[1]):
                                    try:
                                        value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                        if (isinstance(value_data, str) and
                                            software_name.lower() in value_data.lower()):
                                            should_delete = True
                                            break
                                    except Exception:
                                        continue

                                # Check nested keys (like command)
                                if not should_delete:
                                    for k in range(winreg.QueryInfoKey(subkey)[0]):
                                        try:
                                            nested_key_name = winreg.EnumKey(subkey, k)
                                            with winreg.OpenKey(subkey, nested_key_name) as nested_key:
                                                for l in range(winreg.QueryInfoKey(nested_key)[1]):
                                                    try:
                                                        val_name, val_data, _ = winreg.EnumValue(nested_key, l)
                                                        if (isinstance(val_data, str) and
                                                            software_name.lower() in val_data.lower()):
                                                            should_delete = True
                                                            break
                                                    except Exception:
                                                        continue
                                                if should_delete:
                                                    break
                                        except Exception:
                                            continue
                        except Exception:
                            pass

                    if should_delete:
                        subkeys_to_delete.append(subkey_name)

                except Exception:
                    continue

            # Delete identified subkeys
            for subkey_name in subkeys_to_delete:
                try:
                    self._delete_registry_key_recursive(key, subkey_name)
                    self.log(f"Removed context menu entry: {root_name}\\{base_path}\\{subkey_name}")
                    removed_count += 1
                except Exception as e:
                    self.log(f"Error removing context menu key {subkey_name}: {e}")

        except Exception:
            pass

        return removed_count
    
    def clean_firewall_rules(self, software_name):
        """Clean Windows Firewall rules"""
        self.log(f"[Advanced] Cleaning firewall rules for {software_name}...")

        try:
            # Get firewall rules using netsh
            result = subprocess.run(['netsh', 'advfirewall', 'firewall', 'show', 'rule', 'name=all'],
                                  capture_output=True, text=True, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                rules_output = result.stdout
                rules_to_delete = []

                # Parse the output to find rules related to the software
                lines = rules_output.split('\n')
                current_rule = {}

                for line in lines:
                    line = line.strip()
                    if line.startswith('Rule Name:'):
                        if current_rule and 'name' in current_rule:
                            # Check if previous rule should be deleted
                            if (software_name.lower() in current_rule.get('name', '').lower() or
                                software_name.lower() in current_rule.get('program', '').lower()):
                                rules_to_delete.append(current_rule['name'])

                        current_rule = {'name': line.split(':', 1)[1].strip()}
                    elif line.startswith('Program:'):
                        current_rule['program'] = line.split(':', 1)[1].strip()
                    elif line.startswith('Description:'):
                        current_rule['description'] = line.split(':', 1)[1].strip()

                # Check the last rule
                if current_rule and 'name' in current_rule:
                    if (software_name.lower() in current_rule.get('name', '').lower() or
                        software_name.lower() in current_rule.get('program', '').lower()):
                        rules_to_delete.append(current_rule['name'])

                # Delete the identified rules
                removed_count = 0
                for rule_name in rules_to_delete:
                    try:
                        delete_result = subprocess.run(['netsh', 'advfirewall', 'firewall', 'delete', 'rule', f'name={rule_name}'],
                                                     capture_output=True, text=True)
                        if delete_result.returncode == 0:
                            self.log(f"Removed firewall rule: {rule_name}")
                            removed_count += 1
                        else:
                            self.log(f"Failed to remove firewall rule: {rule_name}")
                    except Exception as e:
                        self.log(f"Error removing firewall rule {rule_name}: {e}")

                self.log(f"Removed {removed_count} firewall rules")
            else:
                self.log("Could not retrieve firewall rules")

        except Exception as e:
            self.log(f"Error cleaning firewall rules: {e}")

        self.log(f"[Advanced] Firewall rules cleanup completed for {software_name}")
    
    def clean_network_connections(self, software_name):
        """Clean network connections and profiles"""
        self.log(f"[Advanced] Cleaning network connections for {software_name}...")

        try:
            # Check for active network connections by the software
            connections_killed = 0
            for conn in psutil.net_connections():
                try:
                    if conn.pid:
                        proc = psutil.Process(conn.pid)
                        if software_name.lower() in proc.name().lower():
                            try:
                                proc.terminate()
                                self.log(f"Terminated network connection from process: {proc.name()} (PID: {conn.pid})")
                                connections_killed += 1
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # Clean network adapter settings from registry
            try:
                network_reg_paths = [
                    r"SYSTEM\CurrentControlSet\Control\Network",
                    r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\NetworkCards",
                    r"SYSTEM\CurrentControlSet\Services\Tcpip\Parameters\Interfaces"
                ]

                removed_entries = 0
                for reg_path in network_reg_paths:
                    try:
                        with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, reg_path) as key:
                            removed_entries += self._clean_network_registry_entries(key, software_name, reg_path)
                    except Exception:
                        continue

                self.log(f"Removed {removed_entries} network registry entries")

            except Exception as e:
                self.log(f"Error cleaning network registry: {e}")

            # Clean VPN connections if any
            try:
                result = subprocess.run(['rasdial'], capture_output=True, text=True)
                if result.returncode == 0 and software_name.lower() in result.stdout.lower():
                    # Try to disconnect VPN connections related to the software
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if software_name.lower() in line.lower() and 'Connected' in line:
                            connection_name = line.split()[0]
                            try:
                                subprocess.run(['rasdial', connection_name, '/disconnect'],
                                             capture_output=True, check=False)
                                self.log(f"Disconnected VPN connection: {connection_name}")
                            except Exception:
                                pass
            except Exception:
                pass

            self.log(f"Killed {connections_killed} network connections")

        except Exception as e:
            self.log(f"Error cleaning network connections: {e}")

        self.log(f"[Advanced] Network connections cleanup completed for {software_name}")

    def _clean_network_registry_entries(self, key, software_name, base_path):
        """Helper method to clean network-related registry entries"""
        removed_count = 0
        try:
            subkeys_to_check = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    subkeys_to_check.append(subkey_name)
                except Exception:
                    continue

            for subkey_name in subkeys_to_check:
                try:
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        should_delete = False

                        # Check values in subkey
                        for j in range(winreg.QueryInfoKey(subkey)[1]):
                            try:
                                value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                if (isinstance(value_data, str) and
                                    software_name.lower() in value_data.lower()):
                                    should_delete = True
                                    break
                            except Exception:
                                continue

                        if should_delete:
                            try:
                                self._delete_registry_key_recursive(key, subkey_name)
                                self.log(f"Removed network registry key: {base_path}\\{subkey_name}")
                                removed_count += 1
                            except Exception:
                                pass

                except Exception:
                    continue

        except Exception:
            pass

        return removed_count
    
    def clean_startup_entries(self, software_name):
        """Clean startup entries and autorun programs"""
        self.log(f"[Advanced] Cleaning startup entries for {software_name}...")

        try:
            # Registry paths for startup entries
            startup_paths = [
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunServices",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\RunServicesOnce",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Run",
                r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\RunOnce"
            ]

            removed_count = 0

            # Clean HKEY_LOCAL_MACHINE startup entries
            for startup_path in startup_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, startup_path, 0, winreg.KEY_ALL_ACCESS) as key:
                        removed_count += self._clean_startup_registry_entries(key, software_name, f"HKEY_LOCAL_MACHINE\\{startup_path}")
                except Exception:
                    continue

            # Clean HKEY_CURRENT_USER startup entries
            for startup_path in startup_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_CURRENT_USER, startup_path, 0, winreg.KEY_ALL_ACCESS) as key:
                        removed_count += self._clean_startup_registry_entries(key, software_name, f"HKEY_CURRENT_USER\\{startup_path}")
                except Exception:
                    continue

            # Clean startup folder entries
            startup_folders = [
                os.path.join(os.environ.get('APPDATA', ''), 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup'),
                os.path.join(os.environ.get('ALLUSERSPROFILE', ''), 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup'),
                os.path.join(os.environ.get('PROGRAMDATA', ''), 'Microsoft', 'Windows', 'Start Menu', 'Programs', 'Startup')
            ]

            for folder in startup_folders:
                if os.path.exists(folder):
                    try:
                        for file in os.listdir(folder):
                            if software_name.lower() in file.lower():
                                file_path = os.path.join(folder, file)
                                try:
                                    os.remove(file_path)
                                    self.log(f"Removed startup file: {file_path}")
                                    removed_count += 1
                                except Exception as e:
                                    self.log(f"Error removing startup file {file_path}: {e}")
                    except Exception:
                        continue

            # Clean Task Scheduler startup tasks
            try:
                result = subprocess.run(['schtasks', '/query', '/fo', 'csv'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if software_name.lower() in line.lower():
                            # Extract task name
                            task_name = line.split(',')[0].strip('"')
                            try:
                                subprocess.run(['schtasks', '/delete', '/tn', task_name, '/f'],
                                             capture_output=True, check=False)
                                self.log(f"Removed startup task: {task_name}")
                                removed_count += 1
                            except Exception:
                                pass
            except Exception:
                pass

            self.log(f"Removed {removed_count} startup entries")

        except Exception as e:
            self.log(f"Error cleaning startup entries: {e}")

        self.log(f"[Advanced] Startup entries cleanup completed for {software_name}")

    def _clean_startup_registry_entries(self, key, software_name, base_path):
        """Helper method to clean startup registry entries"""
        removed_count = 0
        try:
            values_to_delete = []
            for i in range(winreg.QueryInfoKey(key)[1]):
                try:
                    value_name, value_data, _ = winreg.EnumValue(key, i)
                    if (isinstance(value_data, str) and
                        (software_name.lower() in value_data.lower() or
                         software_name.lower() in value_name.lower())):
                        values_to_delete.append(value_name)
                except Exception:
                    continue

            for value_name in values_to_delete:
                try:
                    winreg.DeleteValue(key, value_name)
                    self.log(f"Removed startup entry: {base_path}\\{value_name}")
                    removed_count += 1
                except Exception as e:
                    self.log(f"Error removing startup entry {value_name}: {e}")

        except Exception:
            pass

        return removed_count
    
    def clean_system_integration(self, software_name):
        """Clean deep system integration including drivers, system files, and kernel components"""
        self.log(f"[Advanced] Cleaning system integration for {software_name}...")

        try:
            removed_count = 0

            # Clean device drivers
            try:
                result = subprocess.run(['driverquery', '/fo', 'csv'],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # Skip header
                        if software_name.lower() in line.lower():
                            # Extract driver name
                            driver_name = line.split(',')[0].strip('"')
                            self.log(f"Found related driver: {driver_name}")
                            # Note: Actual driver removal requires careful consideration
                            # as it can make the system unstable
            except Exception:
                pass

            # Clean system file associations in deeper registry locations
            system_integration_paths = [
                r"SYSTEM\CurrentControlSet\Services",
                r"SYSTEM\CurrentControlSet\Control\Class",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Setup\PnpLockdownFiles",
                r"SOFTWARE\Microsoft\Windows\CurrentVersion\Setup\PnpResources",
                r"SYSTEM\CurrentControlSet\Enum",
                r"SOFTWARE\Microsoft\Windows NT\CurrentVersion\Drivers32"
            ]

            for sys_path in system_integration_paths:
                try:
                    with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, sys_path) as key:
                        removed_count += self._clean_system_registry_entries(key, software_name, sys_path)
                except Exception:
                    continue

            # Clean Windows Installer cache
            try:
                installer_cache = os.path.join(os.environ.get('WINDIR', ''), 'Installer')
                if os.path.exists(installer_cache):
                    for file in os.listdir(installer_cache):
                        if file.lower().endswith('.msi') or file.lower().endswith('.msp'):
                            file_path = os.path.join(installer_cache, file)
                            try:
                                # Check if MSI is related to our software
                                result = subprocess.run(['msiexec', '/qn', '/a', file_path, 'TARGETDIR=temp'],
                                                      capture_output=True, text=True, timeout=10)
                                if software_name.lower() in result.stdout.lower():
                                    try:
                                        os.remove(file_path)
                                        self.log(f"Removed installer cache file: {file}")
                                        removed_count += 1
                                    except Exception:
                                        pass
                            except (subprocess.TimeoutExpired, Exception):
                                continue
            except Exception:
                pass

            # Clean WinSxS (Side-by-Side) assemblies
            try:
                winsxs_path = os.path.join(os.environ.get('WINDIR', ''), 'WinSxS')
                if os.path.exists(winsxs_path):
                    for item in os.listdir(winsxs_path):
                        if software_name.lower() in item.lower():
                            item_path = os.path.join(winsxs_path, item)
                            self.log(f"Found WinSxS assembly: {item}")
                            # Note: WinSxS cleanup requires extreme caution
                            # Actual removal commented out for safety
                            # removed_count += 1
            except Exception:
                pass

            # Clean system event logs
            try:
                event_logs = ['Application', 'System', 'Security']
                for log_name in event_logs:
                    try:
                        # Clear events related to the software (if any)
                        result = subprocess.run(['wevtutil', 'qe', log_name, '/q:*[System/Provider/@Name="' + software_name + '"]'],
                                              capture_output=True, text=True)
                        if result.returncode == 0 and result.stdout.strip():
                            self.log(f"Found events in {log_name} log related to {software_name}")
                    except Exception:
                        continue
            except Exception:
                pass

            # Clean performance counters
            try:
                result = subprocess.run(['typeperf', '-q'], capture_output=True, text=True)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if software_name.lower() in line.lower():
                            self.log(f"Found performance counter: {line.strip()}")
                            # Performance counter removal requires careful handling
            except Exception:
                pass

            self.log(f"System integration cleanup completed, processed {removed_count} items")

        except Exception as e:
            self.log(f"Error cleaning system integration: {e}")

        self.log(f"[Advanced] System integration cleanup completed for {software_name}")

    def _clean_system_registry_entries(self, key, software_name, base_path):
        """Helper method to clean system-level registry entries"""
        removed_count = 0
        try:
            subkeys_to_check = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    if software_name.lower() in subkey_name.lower():
                        subkeys_to_check.append(subkey_name)
                except Exception:
                    continue

            for subkey_name in subkeys_to_check:
                try:
                    # Be very careful with system registry entries
                    # Only remove if we're confident it's safe
                    if not any(critical in subkey_name.lower() for critical in
                              ['windows', 'microsoft', 'system', 'kernel', 'ntfs', 'tcp']):
                        with winreg.OpenKey(key, subkey_name) as subkey:
                            # Additional verification - check if it really belongs to our software
                            belongs_to_software = False
                            for j in range(winreg.QueryInfoKey(subkey)[1]):
                                try:
                                    value_name, value_data, _ = winreg.EnumValue(subkey, j)
                                    if (isinstance(value_data, str) and
                                        software_name.lower() in value_data.lower()):
                                        belongs_to_software = True
                                        break
                                except Exception:
                                    continue

                            if belongs_to_software:
                                self.log(f"Found system registry entry: {base_path}\\{subkey_name}")
                                # Note: Actual deletion commented out for safety
                                # self._delete_registry_key_recursive(key, subkey_name)
                                # removed_count += 1
                except Exception:
                    continue

        except Exception:
            pass

        return removed_count
    
    def remove_software_services(self, software_name):
        """Stop and remove software-related services"""
        self.log("Stopping and removing services...")
        
        for service in psutil.win_service_iter():
            try:
                service_info = service.as_dict()
                if (software_name.lower() in service_info['name'].lower() or 
                    software_name.lower() in service_info['display_name'].lower()):
                    # Stop service
                    try:
                        subprocess.run(['sc', 'stop', service_info['name']], 
                                     capture_output=True, check=False)
                        self.log(f"Stopped service: {service_info['name']}")
                    except Exception:
                        pass
                    # Delete service
                    try:
                        subprocess.run(['sc', 'delete', service_info['name']], 
                                     capture_output=True, check=False)
                        self.log(f"Deleted service: {service_info['name']}")
                    except Exception as e:
                        self.log(f"Error deleting service {service_info['name']}: {e}")
            except Exception:
                pass
    
    def remove_software_tasks(self, software_name):
        """Remove software-related scheduled tasks"""
        self.log("Removing scheduled tasks...")
        
        try:
            result = subprocess.run(['schtasks', '/query', '/fo', 'csv'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # Skip header
                    if software_name.lower() in line.lower():
                        # Extract task name (first column)
                        task_name = line.split(',')[0].strip('"')
                        try:
                            subprocess.run(['schtasks', '/delete', '/tn', task_name, '/f'], 
                                         capture_output=True, check=False)
                            self.log(f"Deleted scheduled task: {task_name}")
                        except Exception as e:
                            self.log(f"Error deleting task {task_name}: {e}")
        except Exception as e:
            self.log(f"Error removing scheduled tasks: {e}")
    
    def run_official_uninstaller(self, uninstall_string):
        """Run the official uninstaller for the software"""
        self.log("Running official uninstaller...")

        try:
            # Try different silent parameters for different uninstaller types
            silent_params = ['/S', '/silent', '/quiet', '/q', '/SILENT', '/QUIET']

            # First try with original uninstall string
            self.log(f"Attempting: {uninstall_string}")
            result = subprocess.run(uninstall_string, shell=True, capture_output=True,
                                  text=True, timeout=300)

            if result.returncode == 0:
                self.log("Official uninstaller completed successfully")
                return True
            else:
                self.log(f"Official uninstaller returned code: {result.returncode}")
                if result.stderr:
                    self.log(f"Error output: {result.stderr}")

                # Try with different silent parameters
                for param in silent_params:
                    if param not in uninstall_string:
                        try:
                            modified_string = f"{uninstall_string} {param}"
                            self.log(f"Retrying with: {modified_string}")
                            result = subprocess.run(modified_string, shell=True, capture_output=True,
                                                  text=True, timeout=300)
                            if result.returncode == 0:
                                self.log(f"Official uninstaller succeeded with {param}")
                                return True
                        except Exception:
                            continue

                # Try running without shell=True for better compatibility
                try:
                    import shlex
                    args = shlex.split(uninstall_string)
                    result = subprocess.run(args, capture_output=True, text=True, timeout=300)
                    if result.returncode == 0:
                        self.log("Official uninstaller succeeded (direct execution)")
                        return True
                except Exception:
                    pass

                self.log("Official uninstaller failed with all attempts")
                return False

        except subprocess.TimeoutExpired:
            self.log("Official uninstaller timed out")
            return False
        except Exception as e:
            self.log(f"Error running official uninstaller: {e}")
            return False
    
    def force_delete_file(self, file_path):
        """Force delete a file by killing processes, changing permissions, and removing attributes. If still fails, schedule for deletion on reboot."""
        if not os.path.exists(file_path):
            self.log(f"OK: File already deleted: {file_path}")
            return True
        # Try to kill any process using the file
        try:
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    flist = proc.open_files()
                    for f in flist:
                        if os.path.abspath(f.path) == os.path.abspath(file_path):
                            proc.kill()
                            self.log(f"Killed process {proc.name()} (PID: {proc.pid}) using {file_path}")
                except Exception:
                    continue
        except Exception:
            pass
        # Remove read-only, hidden, system attributes (Windows)
        try:
            import ctypes
            FILE_ATTRIBUTE_NORMAL = 0x80
            ctypes.windll.kernel32.SetFileAttributesW(str(file_path), FILE_ATTRIBUTE_NORMAL)
        except Exception:
            pass
        # Take ownership and set permissions
        try:
            import subprocess
            subprocess.run(["takeown", "/F", file_path, "/A", "/R", "/D", "Y"], capture_output=True, check=False)
            subprocess.run(["icacls", file_path, "/grant", "Administrators:F", "/T", "/C"], capture_output=True, check=False)
        except Exception:
            pass
        # Change permissions to allow deletion
        try:
            os.chmod(file_path, 0o777)
        except Exception:
            pass
        # Try to delete
        try:
            os.remove(file_path)
            self.log(f"Force deleted file: {file_path}")
            return True
        except FileNotFoundError:
            self.log(f"OK: File already deleted: {file_path}")
            return True
        except Exception as e:
            # Schedule for deletion on reboot
            try:
                import ctypes
                MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                res = ctypes.windll.kernel32.MoveFileExW(str(file_path), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                if res != 0:
                    self.log(f"OK: Scheduled for deletion on reboot: {file_path}")
                    return True
                else:
                    self.log(f"Failed to force delete file and schedule for deletion: {file_path}: {e}")
                    return False
            except Exception as e2:
                self.log(f"Failed to force delete file and schedule for deletion: {file_path}: {e2}")
                return False

    def _force_remove_error(self, func, path, exc_info):
        """Error handler for shutil.rmtree to force delete files/folders, or schedule for deletion on reboot."""
        try:
            # Try to delete as file or folder
            if os.path.isfile(path) or os.path.islink(path):
                self.force_delete_file(path)
            elif os.path.isdir(path):
                # Try to take ownership and set permissions
                try:
                    subprocess.run(["takeown", "/F", path, "/A", "/R", "/D", "Y"], capture_output=True, check=False)
                    subprocess.run(["icacls", path, "/grant", "Administrators:F", "/T", "/C"], capture_output=True, check=False)
                except Exception:
                    pass
                # Try to delete
                try:
                    shutil.rmtree(path)
                    self.log(f"Force deleted folder: {path}")
                except Exception:
                    # Schedule for deletion on reboot
                    try:
                        import ctypes
                        MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                        res = ctypes.windll.kernel32.MoveFileExW(str(path), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                        if res != 0:
                            self.log(f"OK: Scheduled folder for deletion on reboot: {path}")
                        else:
                            self.log(f"Failed to force delete folder and schedule for deletion: {path}")
                    except Exception as e2:
                        self.log(f"Failed to force delete folder and schedule for deletion: {path}: {e2}")
        except Exception as e:
            self.log(f"Force remove error handler failed for {path}: {e}")

    def _force_remove_error_new(self, path, exc_info):
        """New error handler for shutil.rmtree using onexc parameter"""
        self._force_remove_error(None, path, exc_info)

    def remove_software_files(self, software_name, software_info):
        """Remove software-related files and folders"""
        self.log("Removing software files and folders...")

        # Common locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]

        # Add install location if available
        if 'InstallLocation' in software_info:
            install_location = software_info['InstallLocation'].strip()
            if install_location and install_location not in locations:
                locations.append(install_location)
                self.log(f"Added install location: {install_location}")

        # Also search for folders containing the software name
        search_roots = [
            os.environ.get('PROGRAMFILES', ''),
            os.environ.get('PROGRAMFILES(X86)', ''),
            'C:\\AMSSoftX',  # Custom installation directory
            'C:\\',
            'D:\\'
        ]

        for root in search_roots:
            if os.path.exists(root):
                try:
                    for item in os.listdir(root):
                        if software_name.lower() in item.lower():
                            potential_location = os.path.join(root, item)
                            if os.path.isdir(potential_location) and potential_location not in locations:
                                locations.append(potential_location)
                                self.log(f"Found additional folder: {potential_location}")
                except Exception:
                    continue

        # Remove uninstallers first
        for location in locations:
            if os.path.exists(location):
                try:
                    for file in os.listdir(location):
                        if file.lower().startswith('unins') and file.lower().endswith('.exe'):
                            file_path = os.path.join(location, file)
                            if not self.force_delete_file(file_path):
                                if os.path.exists(file_path):
                                    self.log(f"Could not delete {file_path} even after force attempts.")
                except Exception:
                    continue

        # Remove folders with enhanced force deletion
        removed_count = 0
        for location in locations:
            if os.path.exists(location):
                self.log(f"Attempting to remove: {location}")

                # First try to kill any processes using files in this directory
                self._kill_processes_using_directory(location)

                try:
                    # Use the newer onexc parameter if available, fallback to onerror
                    try:
                        shutil.rmtree(location, onexc=self._force_remove_error_new)
                    except TypeError:
                        # Fallback for older Python versions
                        shutil.rmtree(location, onerror=self._force_remove_error)

                    if not os.path.exists(location):
                        self.log(f"Successfully removed folder: {location}")
                        removed_count += 1
                    else:
                        self.log(f"Folder still exists after removal attempt: {location}")

                except Exception as e:
                    self.log(f"Error removing folder {location}: {e}")
                    # Schedule for deletion on reboot
                    try:
                        import ctypes
                        MOVEFILE_DELAY_UNTIL_REBOOT = 0x4
                        res = ctypes.windll.kernel32.MoveFileExW(str(location), None, MOVEFILE_DELAY_UNTIL_REBOOT)
                        if res != 0:
                            self.log(f"OK: Scheduled folder for deletion on reboot: {location}")
                        else:
                            self.log(f"Failed to schedule folder for deletion: {location}")
                    except Exception as e2:
                        self.log(f"Failed to schedule folder for deletion: {location}: {e2}")

        self.log(f"File removal completed. Removed {removed_count} folders.")

    def _kill_processes_using_directory(self, directory):
        """Kill processes that have files open in the specified directory"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'open_files']):
                try:
                    if proc.info['open_files']:
                        for file_info in proc.info['open_files']:
                            if file_info.path.startswith(directory):
                                try:
                                    process = psutil.Process(proc.info['pid'])
                                    process.terminate()
                                    self.log(f"Terminated process {proc.info['name']} (PID: {proc.info['pid']}) using files in {directory}")
                                    break
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    pass
                except (psutil.AccessDenied, psutil.NoSuchProcess):
                    continue
        except Exception:
            pass
    
    def remove_leftover_uninstallers(self, software_name, software_info):
        """Remove leftover uninstaller executables (unins*.exe) from software folders"""
        self.log("Removing leftover uninstallers (unins*.exe)...")
        # Locations to check
        locations = [
            os.path.join(os.environ.get('PROGRAMFILES', ''), software_name),
            os.path.join(os.environ.get('PROGRAMFILES(X86)', ''), software_name),
            os.path.join(os.environ.get('LOCALAPPDATA', ''), software_name),
            os.path.join(os.environ.get('APPDATA', ''), software_name),
            os.path.join(os.environ.get('PROGRAMDATA', ''), software_name),
        ]
        if 'InstallLocation' in software_info:
            locations.append(software_info['InstallLocation'])
        for location in locations:
            if os.path.exists(location):
                for file in os.listdir(location):
                    if file.lower().startswith('unins') and file.lower().endswith('.exe'):
                        file_path = os.path.join(location, file)
                        if not self.force_delete_file(file_path):
                            if os.path.exists(file_path):
                                self.log(f"Could not delete {file_path} even after force attempts.")
    
    def save_log(self):
        """Save log to file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Log File",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("Log files", "*.log"), ("All files", "*.*")]
            )
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"Deep Uninstaller Pro v{VERSION} - Log Export\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 60 + "\n\n")
                    for message in self.log_messages:
                        f.write(message + "\n")
                self.log(f"✅ Log saved to: {file_path}")
        except Exception as e:
            self.log(f"❌ Error saving log: {e}", "error")

    def copy_log(self):
        """Copy log to clipboard"""
        try:
            log_content = "\n".join(self.log_messages)
            self.root.clipboard_clear()
            self.root.clipboard_append(log_content)
            self.log("✅ Log copied to clipboard")
        except Exception as e:
            self.log(f"❌ Error copying log: {e}", "error")

    def find_in_log(self):
        """Find text in log"""
        search_window = tk.Toplevel(self.root)
        search_window.title("🔍 Find in Log")
        search_window.geometry("350x150")
        search_window.transient(self.root)
        search_window.grab_set()

        # Center the window
        search_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 100,
            self.root.winfo_rooty() + 100
        ))

        # Main frame
        main_frame = ttk.Frame(search_window, padding="15 15 15 15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Search label
        ttk.Label(main_frame, text="Search for:", font=("Segoe UI", 11, "bold")).pack(pady=(0, 8))

        # Search entry
        search_var = tk.StringVar()
        search_entry = ttk.Entry(main_frame, textvariable=search_var, width=35, font=("Segoe UI", 11))
        search_entry.pack(pady=(0, 15), fill=tk.X)
        search_entry.focus()

        def do_search():
            search_text = search_var.get().strip()
            if search_text:
                # Clear previous highlights
                self.log_text.tag_remove("highlight", "1.0", tk.END)

                # Search and highlight
                start = "1.0"
                count = 0
                while True:
                    pos = self.log_text.search(search_text, start, tk.END, nocase=True)
                    if not pos:
                        break
                    end = f"{pos}+{len(search_text)}c"
                    self.log_text.tag_add("highlight", pos, end)
                    start = end
                    count += 1

                # Configure highlight tag
                self.log_text.tag_configure("highlight", background="#ffeb3b", foreground="#000000")

                if count > 0:
                    # Scroll to first match
                    first_match = self.log_text.search(search_text, "1.0", tk.END, nocase=True)
                    self.log_text.see(first_match)
                    self.log(f"✅ Found {count} matches for '{search_text}'")
                else:
                    self.log(f"⚠️ No matches found for '{search_text}'")

                search_window.destroy()
            else:
                # Show message if search text is empty
                messagebox.showwarning("Empty Search", "Please enter text to search for.")

        def clear_highlights():
            self.log_text.tag_remove("highlight", "1.0", tk.END)
            self.log("🔄 Search highlights cleared")
            search_window.destroy()

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        # Search button
        search_button = ttk.Button(button_frame, text="🔍 Search", command=do_search)
        search_button.pack(side=tk.LEFT, padx=(0, 5))

        # Clear button
        clear_button = ttk.Button(button_frame, text="🧹 Clear", command=clear_highlights)
        clear_button.pack(side=tk.LEFT, padx=5)

        # Cancel button
        cancel_button = ttk.Button(button_frame, text="❌ Cancel", command=search_window.destroy)
        cancel_button.pack(side=tk.RIGHT)

        # Bind Enter key to search
        search_entry.bind('<Return>', lambda e: do_search())
        search_entry.bind('<Escape>', lambda e: search_window.destroy())

    def view_scan_report(self):
        """View detailed scan report"""
        if not self.scan_results:
            messagebox.showinfo("No Scan Data", "Please scan software first to view report.")
            return

        report_window = tk.Toplevel(self.root)
        report_window.title("Scan Report")
        report_window.geometry("600x500")
        report_window.transient(self.root)

        # Create report content
        report_text = scrolledtext.ScrolledText(report_window, wrap=tk.WORD, font=("Consolas", 10))
        report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Generate report
        software_name = self.scan_results.get('software_name', 'Unknown')
        report_content = f"SCAN REPORT - {software_name}\n"
        report_content += "=" * 50 + "\n\n"

        for category, items in self.scan_results.items():
            if category != 'software_name' and items:
                report_content += f"{category.upper()}:\n"
                if isinstance(items, list):
                    for item in items:
                        report_content += f"  • {item}\n"
                else:
                    report_content += f"  • {items}\n"
                report_content += "\n"

        report_text.insert(tk.END, report_content)
        report_text.configure(state='disabled')

    def open_settings(self):
        """Open settings dialog"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("⚙️ Settings")
        settings_window.geometry("500x400")
        settings_window.transient(self.root)
        settings_window.grab_set()

        # Center the window
        settings_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 50,
            self.root.winfo_rooty() + 50
        ))

        # Main frame
        main_frame = ttk.Frame(settings_window, padding="20 20 20 20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        ttk.Label(main_frame, text="⚙️ Settings", font=("Segoe UI", 16, "bold")).pack(pady=(0, 20))

        # Settings notebook
        settings_notebook = ttk.Notebook(main_frame)
        settings_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # General settings tab
        general_frame = ttk.Frame(settings_notebook, padding="15 15 15 15")
        settings_notebook.add(general_frame, text="General")

        # Auto-scroll setting
        ttk.Label(general_frame, text="Log Settings:", font=("Segoe UI", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
        ttk.Checkbutton(general_frame, text="Auto-scroll log messages", variable=self.auto_scroll_var).pack(anchor=tk.W, pady=2)

        # Backup settings
        ttk.Label(general_frame, text="Backup Settings:", font=("Segoe UI", 12, "bold")).pack(anchor=tk.W, pady=(20, 10))
        ttk.Checkbutton(general_frame, text="Create registry backup before uninstall", variable=self.create_backup).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(general_frame, text="Enable safe mode (conservative cleanup)", variable=self.safe_mode).pack(anchor=tk.W, pady=2)

        # Advanced settings tab
        advanced_frame = ttk.Frame(settings_notebook, padding="15 15 15 15")
        settings_notebook.add(advanced_frame, text="Advanced")

        ttk.Label(advanced_frame, text="Advanced Options:", font=("Segoe UI", 12, "bold")).pack(anchor=tk.W, pady=(0, 10))
        ttk.Label(advanced_frame, text="• Registry scan depth: 3 levels", font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)
        ttk.Label(advanced_frame, text="• Process termination timeout: 30 seconds", font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)
        ttk.Label(advanced_frame, text="• File deletion retry attempts: 3", font=("Segoe UI", 10)).pack(anchor=tk.W, pady=2)

        # About tab
        about_frame = ttk.Frame(settings_notebook, padding="15 15 15 15")
        settings_notebook.add(about_frame, text="About")

        ttk.Label(about_frame, text=f"Deep Uninstaller Pro v{VERSION}", font=("Segoe UI", 14, "bold")).pack(pady=(0, 10))
        ttk.Label(about_frame, text=f"Build Date: {BUILD_DATE}", font=("Segoe UI", 10)).pack(pady=2)
        ttk.Label(about_frame, text=f"Developer: {DEVELOPER}", font=("Segoe UI", 10)).pack(pady=2)
        ttk.Label(about_frame, text=f"Website: {WEBSITE}", font=("Segoe UI", 10), foreground="blue").pack(pady=2)

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="💾 Save Settings", command=self.save_current_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="🔄 Reset to Defaults", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="❌ Close", command=settings_window.destroy).pack(side=tk.RIGHT)

    def save_current_settings(self):
        """Save current settings"""
        try:
            settings = {
                'auto_scroll': self.auto_scroll_var.get(),
                'create_backup': self.create_backup.get(),
                'safe_mode': self.safe_mode.get(),
                'remove_files': self.remove_files.get(),
                'remove_registry': self.remove_registry.get(),
                'remove_services': self.remove_services.get(),
                'remove_tasks': self.remove_tasks.get(),
                'remove_db': self.remove_db.get(),
                'remove_uninstallers': self.remove_uninstallers.get(),
                'remove_api_hooks': self.remove_api_hooks.get(),
                'remove_dlls': self.remove_dlls.get(),
                'remove_com_objects': self.remove_com_objects.get(),
                'remove_file_associations': self.remove_file_associations.get(),
                'remove_context_menus': self.remove_context_menus.get(),
                'remove_firewall_rules': self.remove_firewall_rules.get(),
                'remove_network_connections': self.remove_network_connections.get(),
                'remove_startup_entries': self.remove_startup_entries.get(),
                'remove_system_integration': self.remove_system_integration.get()
            }

            settings_file = os.path.join(os.path.dirname(__file__), 'settings.json')
            with open(settings_file, 'w') as f:
                json.dump(settings, f, indent=2)

            self.log("✅ Settings saved successfully")
            messagebox.showinfo("Settings", "Settings saved successfully!")
        except Exception as e:
            self.log(f"❌ Error saving settings: {e}")
            messagebox.showerror("Error", f"Error saving settings: {e}")

    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Reset all settings to default values?"):
            # Reset all options to default
            self.auto_scroll_var.set(True)
            self.create_backup.set(True)
            self.safe_mode.set(False)
            self.remove_files.set(True)
            self.remove_registry.set(True)
            self.remove_services.set(True)
            self.remove_tasks.set(True)
            self.remove_db.set(True)
            self.remove_uninstallers.set(True)
            self.remove_api_hooks.set(True)
            self.remove_dlls.set(True)
            self.remove_com_objects.set(True)
            self.remove_file_associations.set(True)
            self.remove_context_menus.set(True)
            self.remove_firewall_rules.set(True)
            self.remove_network_connections.set(True)
            self.remove_startup_entries.set(True)
            self.remove_system_integration.set(True)

            self.log("🔄 Settings reset to defaults")
            messagebox.showinfo("Settings", "Settings reset to default values!")

    def clear_log(self):
        """Clear the uninstall log area and log messages list"""
        self.log_text.delete(1.0, tk.END)
        self.log_messages.clear()
        self.update_status("Ready - Log cleared")
    
    def remove_software_registry(self, software_name):
        """Remove registry entries related to the software"""
        self.log(f"Removing registry entries for {software_name}...")

        removed_count = 0

        # First, remove the main uninstall entry
        uninstall_paths = [
            r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
            r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
        ]

        for uninstall_path in uninstall_paths:
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, uninstall_path, 0, winreg.KEY_ALL_ACCESS) as key:
                    subkeys_to_delete = []
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    display_name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    if display_name == software_name:
                                        subkeys_to_delete.append(subkey_name)
                                        self.log(f"Found uninstall entry: {subkey_name}")
                                except FileNotFoundError:
                                    pass
                        except Exception:
                            continue

                    # Delete the uninstall entries
                    for subkey_name in subkeys_to_delete:
                        try:
                            self._delete_registry_key_recursive(key, subkey_name)
                            self.log(f"Removed uninstall registry key: {uninstall_path}\\{subkey_name}")
                            removed_count += 1
                        except Exception as e:
                            self.log(f"Error removing uninstall key {subkey_name}: {e}")

            except Exception as e:
                self.log(f"Error accessing {uninstall_path}: {e}")

        # Registry roots to check for other entries
        registry_roots = [
            (winreg.HKEY_LOCAL_MACHINE, "HKEY_LOCAL_MACHINE"),
            (winreg.HKEY_CURRENT_USER, "HKEY_CURRENT_USER"),
            (winreg.HKEY_USERS, "HKEY_USERS"),
        ]

        for root, root_name in registry_roots:
            try:
                count = self._remove_registry_recursive(root, "", software_name, root_name, max_depth=3)
                removed_count += count
            except Exception as e:
                self.log(f"Error removing from {root_name}: {e}")

        self.log(f"OK: Registry removal complete for {software_name}. Removed {removed_count} entries.")

    def _remove_registry_recursive(self, key, path, software_name, root_name, max_depth=3):
        """Recursively remove registry entries, returns count of removed entries"""
        if max_depth <= 0:
            return 0

        removed_count = 0
        try:
            subkeys = []
            for i in range(winreg.QueryInfoKey(key)[0]):
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    subkeys.append(subkey_name)
                except Exception:
                    continue

            subkeys_to_delete = []
            for subkey_name in subkeys:
                full_path = f"{root_name}\\{path}\\{subkey_name}" if path else f"{root_name}\\{subkey_name}"
                try:
                    # Check if software name is in subkey name
                    if software_name.lower() in subkey_name.lower():
                        subkeys_to_delete.append(subkey_name)
                        continue

                    # Check subkey values for software references
                    try:
                        with winreg.OpenKey(key, subkey_name, 0, winreg.KEY_READ) as subkey:
                            # Check values in this subkey
                            for j in range(winreg.QueryInfoKey(subkey)[1]):
                                try:
                                    _, value_data, _ = winreg.EnumValue(subkey, j)
                                    if isinstance(value_data, str) and software_name.lower() in value_data.lower():
                                        subkeys_to_delete.append(subkey_name)
                                        break
                                except Exception:
                                    continue

                            # Recurse into subkey if not marked for deletion
                            if subkey_name not in subkeys_to_delete:
                                removed_count += self._remove_registry_recursive(
                                    subkey, f"{path}\\{subkey_name}" if path else subkey_name,
                                    software_name, root_name, max_depth-1
                                )
                    except Exception:
                        continue

                except Exception:
                    continue

            # Delete marked subkeys
            for subkey_name in subkeys_to_delete:
                full_path = f"{root_name}\\{path}\\{subkey_name}" if path else f"{root_name}\\{subkey_name}"
                try:
                    self._delete_registry_key_recursive(key, subkey_name)
                    self.log(f"OK: Deleted registry key: {full_path}")
                    removed_count += 1
                except Exception as e:
                    self.log(f"Error deleting key {full_path}: {e}")

        except Exception:
            pass

        return removed_count

    def clean_database_entries(self, software_name):
        """Clean software-related entries in databases"""
        self.log(f"Cleaning database entries for {software_name}...")

        try:
            removed_count = 0

            # Clean Windows Installer database
            try:
                installer_db_path = os.path.join(os.environ.get('WINDIR', ''), 'Installer')
                if os.path.exists(installer_db_path):
                    for file in os.listdir(installer_db_path):
                        if file.lower().endswith('.msi'):
                            file_path = os.path.join(installer_db_path, file)
                            try:
                                # Check MSI properties
                                result = subprocess.run(['msiexec', '/qn', '/a', file_path, 'TARGETDIR=temp'],
                                                      capture_output=True, text=True, timeout=5)
                                if software_name.lower() in result.stdout.lower():
                                    try:
                                        os.remove(file_path)
                                        self.log(f"Removed MSI database: {file}")
                                        removed_count += 1
                                    except Exception:
                                        pass
                            except (subprocess.TimeoutExpired, Exception):
                                continue
            except Exception:
                pass

            # Clean Windows Registry database entries
            try:
                # Clean MRU (Most Recently Used) lists
                mru_paths = [
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\RecentDocs",
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\RunMRU",
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\ComDlg32\OpenSavePidlMRU"
                ]

                for mru_path in mru_paths:
                    try:
                        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, mru_path, 0, winreg.KEY_ALL_ACCESS) as key:
                            values_to_delete = []
                            for i in range(winreg.QueryInfoKey(key)[1]):
                                try:
                                    value_name, value_data, _ = winreg.EnumValue(key, i)
                                    if isinstance(value_data, (str, bytes)):
                                        data_str = str(value_data).lower()
                                        if software_name.lower() in data_str:
                                            values_to_delete.append(value_name)
                                except Exception:
                                    continue

                            for value_name in values_to_delete:
                                try:
                                    winreg.DeleteValue(key, value_name)
                                    self.log(f"Removed MRU entry: {mru_path}\\{value_name}")
                                    removed_count += 1
                                except Exception:
                                    pass
                    except Exception:
                        continue
            except Exception:
                pass

            # Clean application data databases
            try:
                app_data_paths = [
                    os.environ.get('APPDATA', ''),
                    os.environ.get('LOCALAPPDATA', ''),
                    os.environ.get('PROGRAMDATA', '')
                ]

                for app_data in app_data_paths:
                    if os.path.exists(app_data):
                        software_folder = os.path.join(app_data, software_name)
                        if os.path.exists(software_folder):
                            for root, _, files in os.walk(software_folder):
                                for file in files:
                                    if file.lower().endswith(('.db', '.sqlite', '.sqlite3', '.mdb', '.accdb')):
                                        db_path = os.path.join(root, file)
                                        try:
                                            os.remove(db_path)
                                            self.log(f"Removed database file: {db_path}")
                                            removed_count += 1
                                        except Exception:
                                            pass
            except Exception:
                pass

            # Clean Windows Search database entries
            try:
                search_db_path = os.path.join(os.environ.get('PROGRAMDATA', ''), 'Microsoft', 'Search', 'Data')
                if os.path.exists(search_db_path):
                    self.log(f"Found Windows Search database at: {search_db_path}")
                    # Note: Actual search database cleanup requires careful handling
                    # as it can affect system search functionality
            except Exception:
                pass

            self.log(f"Removed {removed_count} database entries")

        except Exception as e:
            self.log(f"Error cleaning database entries: {e}")

        self.log(f"OK: Database cleaning completed for {software_name}.")

    def create_registry_backup(self):
        """Create registry backup"""
        try:
            backup_path = filedialog.asksaveasfilename(
                title="Save Registry Backup",
                defaultextension=".reg",
                filetypes=[("Registry files", "*.reg"), ("All files", "*.*")]
            )
            if backup_path:
                self.log("🔄 Creating registry backup...")
                result = subprocess.run(['reg', 'export', 'HKLM', backup_path],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.log(f"✅ Registry backup created: {backup_path}")
                else:
                    self.log(f"❌ Failed to create registry backup: {result.stderr}")
        except Exception as e:
            self.log(f"❌ Error creating registry backup: {e}")

    def create_restore_point(self):
        """Create system restore point"""
        try:
            self.log("🔄 Creating system restore point...")
            result = subprocess.run(['powershell', '-Command',
                                   'Checkpoint-Computer -Description "Deep Uninstaller Pro" -RestorePointType "MODIFY_SETTINGS"'],
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.log("✅ System restore point created")
            else:
                self.log(f"❌ Failed to create restore point: {result.stderr}")
        except Exception as e:
            self.log(f"❌ Error creating restore point: {e}")

    def cleanup_temp_files(self):
        """Clean up temporary files"""
        try:
            self.log("🔄 Cleaning temporary files...")
            temp_dirs = [
                os.environ.get('TEMP', ''),
                os.environ.get('TMP', ''),
                os.path.join(os.environ.get('WINDIR', ''), 'Temp')
            ]

            cleaned_count = 0
            for temp_dir in temp_dirs:
                if os.path.exists(temp_dir):
                    for file in os.listdir(temp_dir):
                        try:
                            file_path = os.path.join(temp_dir, file)
                            if os.path.isfile(file_path):
                                os.remove(file_path)
                                cleaned_count += 1
                        except:
                            continue

            self.log(f"✅ Cleaned {cleaned_count} temporary files")
        except Exception as e:
            self.log(f"❌ Error cleaning temp files: {e}")

    def run_disk_cleanup(self):
        """Run Windows Disk Cleanup"""
        try:
            self.log("🔄 Starting Disk Cleanup...")
            subprocess.Popen(['cleanmgr'])
            self.log("✅ Disk Cleanup started")
        except Exception as e:
            self.log(f"❌ Error starting Disk Cleanup: {e}")

    def show_system_info(self):
        """Show system information"""
        info_window = tk.Toplevel(self.root)
        info_window.title("System Information")
        info_window.geometry("500x400")
        info_window.transient(self.root)

        info_text = scrolledtext.ScrolledText(info_window, wrap=tk.WORD, font=("Consolas", 10))
        info_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Get system info
        import platform
        info_content = f"SYSTEM INFORMATION\n"
        info_content += "=" * 30 + "\n\n"
        info_content += f"OS: {platform.system()} {platform.release()}\n"
        info_content += f"Version: {platform.version()}\n"
        info_content += f"Architecture: {platform.architecture()[0]}\n"
        info_content += f"Processor: {platform.processor()}\n"
        info_content += f"Machine: {platform.machine()}\n"
        info_content += f"Python: {platform.python_version()}\n\n"

        # Memory info
        try:
            memory = psutil.virtual_memory()
            info_content += f"Total RAM: {memory.total // (1024**3)} GB\n"
            info_content += f"Available RAM: {memory.available // (1024**3)} GB\n"
            info_content += f"RAM Usage: {memory.percent}%\n\n"
        except:
            pass

        # Disk info
        try:
            disk = psutil.disk_usage('C:')
            info_content += f"C: Drive Total: {disk.total // (1024**3)} GB\n"
            info_content += f"C: Drive Free: {disk.free // (1024**3)} GB\n"
            info_content += f"C: Drive Usage: {(disk.used / disk.total) * 100:.1f}%\n"
        except:
            pass

        info_text.insert(tk.END, info_content)
        info_text.configure(state='disabled')

    def show_installed_programs(self):
        """Show list of installed programs"""
        programs_window = tk.Toplevel(self.root)
        programs_window.title("Installed Programs")
        programs_window.geometry("600x500")
        programs_window.transient(self.root)

        programs_text = scrolledtext.ScrolledText(programs_window, wrap=tk.NONE, font=("Consolas", 10))
        programs_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        content = f"INSTALLED PROGRAMS ({len(self.software_list)})\n"
        content += "=" * 40 + "\n\n"

        for i, program in enumerate(self.software_list, 1):
            content += f"{i:3d}. {program}\n"

        programs_text.insert(tk.END, content)
        programs_text.configure(state='disabled')

    def load_settings(self):
        """Load settings from file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Load Settings",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if file_path:
                with open(file_path, 'r') as f:
                    _ = json.load(f)  # Settings loading implementation pending
                # Apply settings (implementation pending)
                self.log(f"✅ Settings loaded from: {file_path}")
        except Exception as e:
            self.log(f"❌ Error loading settings: {e}")

    def save_settings(self):
        """Save current settings to file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="Save Settings",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            if file_path:
                settings = {
                    'remove_files': self.remove_files.get(),
                    'remove_registry': self.remove_registry.get(),
                    'remove_services': self.remove_services.get(),
                    # Add other settings...
                }
                with open(file_path, 'w') as f:
                    json.dump(settings, f, indent=2)
                self.log(f"✅ Settings saved to: {file_path}")
        except Exception as e:
            self.log(f"❌ Error saving settings: {e}")

    def show_help(self):
        """Show help dialog"""
        help_window = tk.Toplevel(self.root)
        help_window.title("User Guide")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        help_text = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, font=("Segoe UI", 10))
        help_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_content = f"""DEEP UNINSTALLER PRO v{VERSION} - USER GUIDE

OVERVIEW
========
Deep Uninstaller Pro is a comprehensive software removal tool that completely
removes all traces of installed programs from your Windows system.

BASIC USAGE
===========
1. Select software from the dropdown list
2. Choose uninstall options (Basic/Advanced tabs)
3. Click "Scan Software" to analyze what will be removed
4. Click "Complete Uninstall" to remove the software

FEATURES
========
• Complete file and folder removal
• Deep registry scanning and cleanup
• Service and scheduled task removal
• Advanced system integration cleanup
• Real-time operation logging
• Registry backup creation
• System restore point creation

SAFETY TIPS
===========
• Always create a backup before uninstalling
• Run as Administrator for full functionality
• Close the target software before uninstalling
• Review scan results before proceeding

KEYBOARD SHORTCUTS
==================
F1 - Show this help
F5 - Refresh software list
Ctrl+S - Save log

SUPPORT
=======
Website: {WEBSITE}
Version: {VERSION}
Build Date: {BUILD_DATE}
"""

        help_text.insert(tk.END, help_content)
        help_text.configure(state='disabled')

    def show_shortcuts(self):
        """Show keyboard shortcuts"""
        shortcuts_window = tk.Toplevel(self.root)
        shortcuts_window.title("Keyboard Shortcuts")
        shortcuts_window.geometry("400x300")
        shortcuts_window.transient(self.root)

        shortcuts_text = scrolledtext.ScrolledText(shortcuts_window, wrap=tk.WORD, font=("Consolas", 10))
        shortcuts_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        shortcuts_content = """KEYBOARD SHORTCUTS

F1          Show Help
F5          Refresh Software List
Ctrl+S      Save Log
Alt+F4      Exit Application
Ctrl+F      Find in Log
Ctrl+C      Copy Log (when log is focused)

MOUSE SHORTCUTS

Double-click software name - Quick scan
Right-click log area - Context menu
"""

        shortcuts_text.insert(tk.END, shortcuts_content)
        shortcuts_text.configure(state='disabled')

    def check_updates(self):
        """Check for updates"""
        self.log("🔄 Checking for updates...")
        # Placeholder for update checking
        messagebox.showinfo("Updates", f"You are running the latest version ({VERSION})")

    def show_about(self):
        """Show about dialog"""
        about_window = tk.Toplevel(self.root)
        about_window.title("About")
        about_window.geometry("400x350")
        about_window.transient(self.root)
        about_window.grab_set()

        # Center the window
        about_window.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # About content
        about_frame = ttk.Frame(about_window, padding="20 20 20 20")
        about_frame.pack(fill=tk.BOTH, expand=True)

        ttk.Label(about_frame, text="Deep Uninstaller Pro",
                 font=("Segoe UI", 18, "bold")).pack(pady=(0, 10))

        ttk.Label(about_frame, text=f"Version {VERSION}",
                 font=("Segoe UI", 12)).pack()

        ttk.Label(about_frame, text=f"Build Date: {BUILD_DATE}",
                 font=("Segoe UI", 10)).pack(pady=(5, 15))

        ttk.Label(about_frame, text=f"Developed by {DEVELOPER}",
                 font=("Segoe UI", 12, "bold")).pack(pady=(0, 5))

        website_label = ttk.Label(about_frame, text=WEBSITE,
                                 font=("Segoe UI", 10), foreground="blue", cursor="hand2")
        website_label.pack(pady=(0, 15))
        website_label.bind("<Button-1>", lambda _: webbrowser.open(WEBSITE))

        ttk.Label(about_frame, text="Complete Software Removal Tool",
                 font=("Segoe UI", 11)).pack(pady=(0, 10))

        ttk.Label(about_frame, text="© 2025 AMSSoftX. All rights reserved.",
                 font=("Segoe UI", 9)).pack(pady=(10, 0))

        ttk.Button(about_frame, text="Close", command=about_window.destroy).pack(pady=(20, 0))

if __name__ == "__main__":
    try:
        app = CompleteSoftwareUninstaller()
        app.root.mainloop()
    except Exception as e:
        print(f"Error starting application: {e}")
        input("Press Enter to exit...")